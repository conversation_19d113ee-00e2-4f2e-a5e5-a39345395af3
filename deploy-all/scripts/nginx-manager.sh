#!/bin/bash

# =============================================================================
# Nginx配置管理脚本 - 统一管理所有项目的Nginx配置
# =============================================================================

set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
NEW_API_DIR="$WORKSPACE_DIR/new-api"
LOVE_DIR="$WORKSPACE_DIR/love"
SHARED_DIR="$WORKSPACE_DIR/shared"

# 加载公共模块
source "$SCRIPT_DIR/../common/colors.sh"

# 手动加载需要的工具函数，避免路径冲突
check_root() {
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}[错误]${NC} 此脚本必须以root权限运行"
        echo -e "${YELLOW}请使用: ${GREEN}sudo $0${NC}"
        exit 1
    fi
}

print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Nginx 配置管理系统                        ║"
    echo "║              统一管理所有项目的Nginx配置                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

show_environment_info() {
    echo -e "${YELLOW}📍 环境信息:${NC}"
    echo -e "   • 当前用户: ${GREEN}$(whoami)${NC}"
    echo -e "   • 工作空间: ${GREEN}$WORKSPACE_DIR${NC}"
    echo
}

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_section() {
    echo -e "\n${CYAN}=== $1 ===${NC}"
}

log_check_success() {
    echo -e "   ${GREEN}✓${NC} $1"
}

log_check_error() {
    echo -e "   ${RED}✗${NC} $1"
}

log_check_warning() {
    echo -e "   ${YELLOW}⚠${NC} $1"
}

# 确保目录存在
ensure_directory() {
    local dir="$1"
    local desc="$2"

    if [[ ! -d "$dir" ]]; then
        log_info "创建$desc: $dir"
        mkdir -p "$dir"
    fi
}

# 备份文件
backup_file() {
    local file="$1"
    local backup_dir="${2:-$BACKUP_DIR}"

    if [[ -f "$file" ]]; then
        ensure_directory "$backup_dir" "备份目录"
        local backup_name="$(basename "$file").backup.$(date +%Y%m%d-%H%M%S)"
        local backup_path="$backup_dir/$backup_name"

        cp "$file" "$backup_path"
        log_info "文件已备份: $backup_path"
        return 0
    else
        log_warning "文件不存在，无法备份: $file"
        return 1
    fi
}

check_port() {
    local port="$1"
    local service_name="$2"

    if ss -tlnp | grep ":$port " > /dev/null; then
        local process=$(ss -tlnp | grep ":$port " | awk '{print $6}' | cut -d'=' -f2 | cut -d',' -f1)
        echo -e "${YELLOW}[⚠]${NC} 端口 $port ($service_name) 被占用 (进程: ${process:-未知})"
        return 1
    else
        echo -e "${GREEN}[✓]${NC} 端口 $port ($service_name) 可用"
        return 0
    fi
}

backup_file() {
    local file_path="$1"
    local backup_suffix="${2:-.backup.$(date +%Y%m%d_%H%M%S)}"

    if [[ -f "$file_path" ]]; then
        local backup_path="${file_path}${backup_suffix}"
        cp "$file_path" "$backup_path"
        echo -e "${BLUE}[信息]${NC} 已备份: $file_path -> $backup_path"
    fi
}

ensure_directory() {
    local dir_path="$1"
    local description="$2"

    if [[ ! -d "$dir_path" ]]; then
        echo -e "${BLUE}[信息]${NC} 创建目录: $description"
        mkdir -p "$dir_path"
        chmod 755 "$dir_path"
    fi
}

# 配置文件路径
NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
MAIN_CONFIG_FILE="$NGINX_SITES_AVAILABLE/liangliangdamowang.edu.deal.conf"
MAIN_CONFIG_LINK="$NGINX_SITES_ENABLED/liangliangdamowang.edu.deal.conf"

# 新架构配置路径
SHARED_NGINX_DIR="$WORKSPACE_DIR/shared/nginx"
NGINX_CONF_D="$SHARED_NGINX_DIR/conf.d"
SNIPPETS_DIR="$SHARED_NGINX_DIR/snippets"
TEMPLATES_DIR="$SHARED_NGINX_DIR/templates"
SCRIPTS_DIR="$SHARED_NGINX_DIR/scripts"
BACKUP_DIR="$SHARED_NGINX_DIR/backup"

# 显示帮助信息
show_help() {
    echo "Nginx配置管理脚本 - 统一管理所有项目的Nginx配置"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  --generate      生成统一的Nginx配置"
    echo "  --apply         应用配置并重载Nginx"
    echo "  --test          测试Nginx配置语法"
    echo "  --backup        备份当前配置"
    echo "  --restore       恢复备份配置"
    echo "  --status        显示Nginx状态和配置信息"
    echo "  --conflicts     检查配置冲突"
    echo "  --clean         清理冲突的配置文件"
    echo
    echo "项目管理选项:"
    echo "  --add-project <domain> <name> <template>  添加新项目配置"
    echo "  --enable-site <domain>                    启用站点"
    echo "  --disable-site <domain>                   禁用站点"
    echo "  --list-templates                          列出可用模板"
    echo "  --ssl-cert <domain>                       申请SSL证书"
    echo "  --help, -h                                显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 --generate --apply                    # 生成并应用配置"
    echo "  $0 --conflicts                           # 检查配置冲突"
    echo "  $0 --status                              # 查看状态"
    echo "  $0 --add-project example.com myapp spa   # 添加SPA项目"
    echo "  $0 --enable-site example.com             # 启用站点"
    echo "  $0 --ssl-cert example.com                # 申请SSL证书"
}

# 显示增强帮助信息
show_enhanced_help() {
    echo "Nginx配置管理脚本 - 统一管理所有项目的Nginx配置 (增强版)"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "基础选项:"
    echo "  --generate      生成统一的Nginx配置"
    echo "  --apply         应用配置并重载Nginx"
    echo "  --test          测试Nginx配置语法"
    echo "  --backup        备份当前配置"
    echo "  --restore       恢复备份配置"
    echo "  --status        显示Nginx状态和配置信息"
    echo "  --conflicts     检查配置冲突"
    echo "  --clean         清理冲突的配置文件"
    echo
    echo "项目管理选项:"
    echo "  --add-project <domain> <name> <template>  添加新项目配置"
    echo "  --enable-site <domain>                    启用站点"
    echo "  --disable-site <domain>                   禁用站点"
    echo "  --list-templates                          列出可用模板"
    echo "  --create-from-template <template> <name> <domain> [options]  从模板创建配置"
    echo
    echo "SSL证书管理:"
    echo "  --ssl-cert <domain> [email]               申请SSL证书"
    echo "  --ssl-renew [domain]                      续期SSL证书"
    echo "  --ssl-status [domain]                     查看SSL证书状态"
    echo "  --ssl-auto-renew                          设置自动续期"
    echo
    echo "批量操作:"
    echo "  --batch-ssl-renew                         批量续期所有证书"
    echo "  --batch-enable <pattern>                  批量启用站点"
    echo "  --batch-disable <pattern>                 批量禁用站点"
    echo "  --batch-backup                            批量备份所有配置"
    echo
    echo "配置转换和验证:"
    echo "  --convert-config <file>                   转换传统配置为模块化配置"
    echo "  --validate-config [file]                  验证配置文件"
    echo "  --analyze-performance                     分析配置性能"
    echo "  --check-security                          安全配置检查"
    echo
    echo "高级功能:"
    echo "  --help, -h                                显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 --generate --apply                    # 生成并应用配置"
    echo "  $0 --create-from-template spa myapp app.example.com  # 从模板创建SPA项目"
    echo "  $0 --ssl-cert example.com <EMAIL>  # 申请SSL证书"
    echo "  $0 --batch-ssl-renew                     # 批量续期证书"
    echo "  $0 --convert-config /path/to/old.conf    # 转换配置文件"
    echo "  $0 --analyze-performance                 # 性能分析"
    echo "  $0 --check-security                      # 安全检查"
}

# ===== 新增SSL证书管理功能 =====

# SSL证书续期
ssl_renew() {
    local domain="$1"

    log_section "SSL证书续期"

    # 检查certbot是否安装
    if ! command -v certbot &> /dev/null; then
        log_error "certbot未安装，请先安装certbot"
        return 1
    fi

    if [[ -n "$domain" ]]; then
        # 续期指定域名
        log_info "续期域名: $domain"
        if certbot renew --cert-name "$domain"; then
            log_success "域名 $domain 证书续期成功"
            systemctl reload nginx
        else
            log_error "域名 $domain 证书续期失败"
            return 1
        fi
    else
        # 续期所有证书
        log_info "续期所有SSL证书..."
        if certbot renew; then
            log_success "所有证书续期完成"
            systemctl reload nginx
        else
            log_error "证书续期失败"
            return 1
        fi
    fi
}

# SSL证书状态查看
ssl_status() {
    local domain="$1"

    log_section "SSL证书状态"

    if [[ -n "$domain" ]]; then
        # 查看指定域名证书状态
        log_info "查看域名: $domain"
        if [[ -d "/etc/letsencrypt/live/$domain" ]]; then
            local cert_file="/etc/letsencrypt/live/$domain/fullchain.pem"
            local expiry_date=$(openssl x509 -enddate -noout -in "$cert_file" | cut -d= -f2)
            local days_left=$(( ($(date -d "$expiry_date" +%s) - $(date +%s)) / 86400 ))

            echo -e "   • 证书文件: $cert_file"
            echo -e "   • 过期时间: $expiry_date"

            if [[ $days_left -gt 30 ]]; then
                echo -e "   • 剩余天数: ${GREEN}$days_left 天${NC}"
            elif [[ $days_left -gt 7 ]]; then
                echo -e "   • 剩余天数: ${YELLOW}$days_left 天${NC}"
            else
                echo -e "   • 剩余天数: ${RED}$days_left 天 (需要续期)${NC}"
            fi
        else
            log_error "域名 $domain 没有SSL证书"
            return 1
        fi
    else
        # 查看所有证书状态
        log_info "查看所有SSL证书状态..."
        if command -v certbot &> /dev/null; then
            certbot certificates
        else
            log_error "certbot未安装"
            return 1
        fi
    fi
}

# 设置SSL自动续期
ssl_auto_renew() {
    log_section "设置SSL自动续期"

    # 检查cron服务
    if ! systemctl is-active --quiet cron; then
        log_info "启动cron服务..."
        systemctl start cron
        systemctl enable cron
    fi

    # 添加crontab任务
    local cron_job="0 12 * * * /usr/bin/certbot renew --quiet && /usr/bin/systemctl reload nginx"

    # 检查是否已存在
    if crontab -l 2>/dev/null | grep -q "certbot renew"; then
        log_info "SSL自动续期任务已存在"
    else
        # 添加新的cron任务
        (crontab -l 2>/dev/null; echo "$cron_job") | crontab -
        log_success "SSL自动续期任务已添加 (每天12:00执行)"
    fi

    # 测试续期
    log_info "测试证书续期..."
    if certbot renew --dry-run; then
        log_success "证书续期测试通过"
    else
        log_warning "证书续期测试失败，请检查配置"
    fi
}

# 检查配置冲突
check_conflicts() {
    log_info "检查Nginx配置冲突..."
    
    local conflicts_found=false
    
    # 检查多个配置文件
    local config_files=(
        "$WORKSPACE_DIR/shared/nginx/system-nginx.conf"
        "$WORKSPACE_DIR/love/nginx-love/nginx-complete.conf"
        "$WORKSPACE_DIR/new-api/nginx-newapi/system-nginx.conf"
        "$WORKSPACE_DIR/new-api/nginx-newapi/system-nginx-http.conf"
    )
    
    echo -e "${CYAN}📋 发现的配置文件：${NC}"
    for config_file in "${config_files[@]}"; do
        if [[ -f "$config_file" ]]; then
            local size=$(du -h "$config_file" | cut -f1)
            echo -e "   • $(basename "$config_file") - $size - $config_file"
            conflicts_found=true
        fi
    done
    
    # 检查系统配置
    echo -e "\n${CYAN}📋 系统Nginx配置：${NC}"
    if [[ -f "$MAIN_CONFIG_FILE" ]]; then
        local size=$(du -h "$MAIN_CONFIG_FILE" | cut -f1)
        echo -e "   • 主配置文件: $size - $MAIN_CONFIG_FILE"
    else
        echo -e "   • 主配置文件: ${RED}不存在${NC}"
    fi
    
    if [[ -L "$MAIN_CONFIG_LINK" ]]; then
        local target=$(readlink "$MAIN_CONFIG_LINK")
        echo -e "   • 启用链接: $target"
    else
        echo -e "   • 启用链接: ${RED}不存在${NC}"
    fi
    
    # 检查端口冲突
    echo -e "\n${CYAN}📋 端口使用情况：${NC}"
    check_port 80 "HTTP"
    check_port 443 "HTTPS"
    check_port 3000 "New-API"
    check_port 1314 "Love API"
    
    # 检查Nginx进程
    echo -e "\n${CYAN}📋 Nginx服务状态：${NC}"
    if systemctl is-active --quiet nginx; then
        echo -e "   • Nginx服务: ${GREEN}运行中${NC}"
        
        # 检查配置语法
        if nginx -t &>/dev/null; then
            echo -e "   • 配置语法: ${GREEN}正确${NC}"
        else
            echo -e "   • 配置语法: ${RED}错误${NC}"
            conflicts_found=true
        fi
    else
        echo -e "   • Nginx服务: ${RED}未运行${NC}"
    fi
    
    if [[ "$conflicts_found" == "true" ]]; then
        log_warning "发现配置冲突或问题"
        return 1
    else
        log_success "未发现明显冲突"
        return 0
    fi
}

# 清理冲突配置
clean_conflicts() {
    log_info "清理冲突的Nginx配置..."
    
    # 备份当前配置
    if [[ -f "$MAIN_CONFIG_FILE" ]]; then
        backup_file "$MAIN_CONFIG_FILE"
    fi
    
    # 删除可能冲突的链接
    rm -f "$NGINX_SITES_ENABLED/default"
    rm -f "$NGINX_SITES_ENABLED/000-default"
    
    # 禁用其他可能的配置
    for config in "$NGINX_SITES_ENABLED"/*; do
        if [[ -f "$config" ]] && [[ "$(basename "$config")" != "liangliangdamowang.edu.deal.conf" ]]; then
            log_info "禁用配置: $(basename "$config")"
            rm -f "$config"
        fi
    done
    
    log_success "冲突配置清理完成"
}

# 生成统一配置
generate_unified_config() {
    log_info "生成统一的Nginx配置..."
    
    # 确保目录存在
    ensure_directory "$NGINX_SITES_AVAILABLE" "Nginx配置目录"
    ensure_directory "$NGINX_SITES_ENABLED" "Nginx启用目录"
    
    # 生成配置文件
    cat > "$MAIN_CONFIG_FILE" << EOF
# Unified Nginx configuration for liangliangdamowang.edu.deal
# Generated by Deploy-All Nginx Manager
# $(date)

# Upstream definitions
upstream new_api_backend {
    server 127.0.0.1:3000;
    keepalive 32;
}

upstream love_backend {
    server 127.0.0.1:1314;
    keepalive 32;
}

server {
    listen 80;
    server_name liangliangdamowang.edu.deal;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # ===== LOVE SITE CONFIGURATIONS =====
    
    # Love site main page
    location = /love/ {
        alias $WORKSPACE_DIR/love/html/;
        try_files /index.html =404;
    }
    
    location = /love {
        return 301 \$scheme://\$host/love/;
    }

    # Love site clean URLs (支持多页面扩展)
    location ~ ^/love/(together-days|anniversary|meetings|memorial|future-page1|future-page2)\$ {
        alias $WORKSPACE_DIR/love/html/;
        try_files /\$1.html =404;
    }

    # Love site static files (CSS/JS)
    location ~ ^/love/(style\.css|pages\.css|script\.js|romantic-quotes\.js)\$ {
        alias $WORKSPACE_DIR/love/\$1;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Love site background files
    location /love/background/ {
        alias $WORKSPACE_DIR/love/background/;
        try_files \$uri \$uri/ =404;
        
        location ~* \.(mp4|webm|ogg|avi|mov|wmv|flv|mkv)\$ {
            add_header Accept-Ranges bytes;
            add_header Cache-Control "public, max-age=31536000";
            expires 1y;
        }
    }

    # Love site fonts
    location /love/fonts/ {
        alias $WORKSPACE_DIR/love/fonts/;
        try_files \$uri =404;
        
        location ~* \.(ttf|otf|woff|woff2|eot)\$ {
            add_header Access-Control-Allow-Origin "*";
            expires 1y;
        }
    }

    # Love site API routes
    location /love/api/ {
        rewrite ^/love/api/(.*) /api/\$1 break;
        proxy_pass http://love_backend;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # CORS headers
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
    }

    # ===== NEW-API CONFIGURATIONS =====
    
    # New-API service at /new-api path (优化后的路径结构)
    location /new-api/ {
        rewrite ^/new-api/(.*) /\$1 break;
        proxy_pass http://new_api_backend;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;
        
        # Buffer settings for streaming responses
        proxy_buffering off;
        proxy_cache off;
    }
    
    # Redirect /new-api to /new-api/
    location = /new-api {
        return 301 \$scheme://\$host/new-api/;
    }

    # ===== ROOT CONFIGURATIONS =====
    
    # Welcome page for root path
    location = / {
        return 200 '<!DOCTYPE html>
<html>
<head>
    <title>Welcome to liangliangdamowang.edu.deal</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .container { max-width: 600px; margin: 0 auto; background: rgba(255,255,255,0.1); padding: 40px; border-radius: 15px; backdrop-filter: blur(10px); }
        .service-link { display: inline-block; margin: 20px; padding: 15px 30px; 
                       background: rgba(255,255,255,0.2); color: white; text-decoration: none; 
                       border-radius: 25px; border: 2px solid rgba(255,255,255,0.3); transition: all 0.3s; }
        .service-link:hover { background: rgba(255,255,255,0.3); transform: translateY(-2px); }
        h1 { margin-bottom: 30px; font-size: 2.5em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Welcome to Our Platform</h1>
        <p>Choose a service to access:</p>
        <a href="/new-api/" class="service-link">🚀 New-API Platform</a>
        <a href="/love/" class="service-link">💕 Love Website</a>
    </div>
</body>
</html>';
        add_header Content-Type text/html;
    }
    
    # Fallback for other root paths - redirect to welcome page
    location / {
        return 301 \$scheme://\$host/;
    }
}
EOF

    log_success "统一配置生成完成: $MAIN_CONFIG_FILE"
}

# 应用配置
apply_config() {
    log_info "应用Nginx配置..."
    
    # 清理冲突
    clean_conflicts
    
    # 创建启用链接
    ln -sf "$MAIN_CONFIG_FILE" "$MAIN_CONFIG_LINK"
    
    # 测试配置
    if nginx -t; then
        log_success "配置语法测试通过"
        
        # 重载配置
        if systemctl reload nginx; then
            log_success "Nginx配置已重载"
        else
            log_error "Nginx重载失败"
        fi
    else
        log_error "Nginx配置语法错误"
        nginx -t
        return 1
    fi
}

# 显示状态
show_status() {
    log_section "Nginx配置状态"
    
    # 服务状态
    if systemctl is-active --quiet nginx; then
        log_check_success "Nginx服务运行正常"
    else
        log_check_error "Nginx服务未运行"
    fi
    
    # 配置文件状态
    if [[ -f "$MAIN_CONFIG_FILE" ]]; then
        local size=$(du -h "$MAIN_CONFIG_FILE" | cut -f1)
        log_check_success "主配置文件存在 ($size)"
    else
        log_check_error "主配置文件不存在"
    fi
    
    if [[ -L "$MAIN_CONFIG_LINK" ]]; then
        log_check_success "配置已启用"
    else
        log_check_error "配置未启用"
    fi
    
    # 配置语法
    if nginx -t &>/dev/null; then
        log_check_success "配置语法正确"
    else
        log_check_error "配置语法错误"
    fi
    
    # 访问测试
    echo -e "\n${CYAN}🌐 访问测试：${NC}"
    echo -e "   • 主页: ${YELLOW}http://liangliangdamowang.edu.deal${NC}"
    echo -e "   • New-API: ${YELLOW}http://liangliangdamowang.edu.deal/new-api${NC}"
    echo -e "   • Love网站: ${YELLOW}http://liangliangdamowang.edu.deal/love${NC}"
}

# ===== 新增项目管理功能 =====

# 列出可用模板
list_templates() {
    log_section "可用配置模板"

    if [[ -d "$TEMPLATES_DIR" ]]; then
        echo -e "${CYAN}📋 模板列表：${NC}"
        for template in "$TEMPLATES_DIR"/*.tpl; do
            if [[ -f "$template" ]]; then
                local name=$(basename "$template" .tpl)
                echo -e "   • ${GREEN}$name${NC} - $(head -2 "$template" | tail -1 | sed 's/^# //')"
            fi
        done
    else
        log_error "模板目录不存在: $TEMPLATES_DIR"
        return 1
    fi
}

# 添加新项目
add_project() {
    local domain="$1"
    local project_name="$2"
    local template="$3"
    local backend_host="${4:-127.0.0.1}"
    local backend_port="$5"
    local location_path="$6"

    # 参数验证
    if [[ -z "$domain" || -z "$project_name" || -z "$template" ]]; then
        log_error "缺少必需参数"
        echo "用法: --add-project <domain> <project_name> <template> [backend_host] [backend_port] [location_path]"
        return 1
    fi

    log_section "添加新项目: $project_name"

    # 检查模板是否存在
    local template_file="$TEMPLATES_DIR/${template}.tpl"
    if [[ ! -f "$template_file" ]]; then
        log_error "模板不存在: $template"
        list_templates
        return 1
    fi

    # 设置默认值
    [[ -z "$backend_port" ]] && backend_port="3000"
    [[ -z "$location_path" ]] && location_path="/"

    # 生成配置文件
    local config_file="$NGINX_CONF_D/${domain}.conf"
    log_info "生成配置文件: $config_file"

    # 复制模板并替换占位符
    cp "$template_file" "$config_file"
    sed -i "s/__DOMAIN__/$domain/g" "$config_file"
    sed -i "s/__PROJECT_NAME__/$project_name/g" "$config_file"
    sed -i "s/__BACKEND_HOST__/$backend_host/g" "$config_file"
    sed -i "s/__BACKEND_PORT__/$backend_port/g" "$config_file"
    sed -i "s|__LOCATION_PATH__|$location_path|g" "$config_file"
    sed -i "s|__PROJECT_ROOT__|/var/www/$project_name|g" "$config_file"

    # 创建项目目录
    local project_root="/var/www/$project_name"
    if [[ ! -d "$project_root" ]]; then
        log_info "创建项目目录: $project_root"
        mkdir -p "$project_root"
        chown -R www-data:www-data "$project_root"
    fi

    # 测试配置
    if nginx -t; then
        log_success "配置文件生成成功: $config_file"
        log_info "请运行 --apply 来应用配置"
    else
        log_error "配置语法错误，删除生成的文件"
        rm -f "$config_file"
        return 1
    fi
}

# 启用站点
enable_site() {
    local domain="$1"

    if [[ -z "$domain" ]]; then
        log_error "缺少域名参数"
        echo "用法: --enable-site <domain>"
        return 1
    fi

    log_section "启用站点: $domain"

    local config_file="$NGINX_CONF_D/${domain}.conf"
    local available_file="$NGINX_SITES_AVAILABLE/${domain}.conf"
    local enabled_file="$NGINX_SITES_ENABLED/${domain}.conf"

    # 检查配置文件是否存在
    if [[ -f "$config_file" ]]; then
        # 新架构：conf.d目录
        if nginx -t; then
            log_success "站点 $domain 已启用 (conf.d架构)"
        else
            log_error "配置语法错误"
            return 1
        fi
    elif [[ -f "$available_file" ]]; then
        # 传统架构：sites-available/sites-enabled
        if [[ -L "$enabled_file" ]]; then
            log_info "站点 $domain 已经启用"
        else
            ln -s "$available_file" "$enabled_file"
            if nginx -t && systemctl reload nginx; then
                log_success "站点 $domain 已启用"
            else
                log_error "启用失败，回滚操作"
                rm -f "$enabled_file"
                return 1
            fi
        fi
    else
        log_error "配置文件不存在: $domain"
        return 1
    fi
}

# 禁用站点
disable_site() {
    local domain="$1"

    if [[ -z "$domain" ]]; then
        log_error "缺少域名参数"
        echo "用法: --disable-site <domain>"
        return 1
    fi

    log_section "禁用站点: $domain"

    local config_file="$NGINX_CONF_D/${domain}.conf"
    local enabled_file="$NGINX_SITES_ENABLED/${domain}.conf"

    # 检查新架构
    if [[ -f "$config_file" ]]; then
        # 新架构：移动到备份目录
        local backup_file="$BACKUP_DIR/disabled-$(date +%Y%m%d-%H%M%S)-${domain}.conf"
        mv "$config_file" "$backup_file"

        if nginx -t && systemctl reload nginx; then
            log_success "站点 $domain 已禁用，配置已备份到: $backup_file"
        else
            log_error "禁用失败，恢复配置"
            mv "$backup_file" "$config_file"
            return 1
        fi
    elif [[ -L "$enabled_file" ]]; then
        # 传统架构：删除软链接
        rm -f "$enabled_file"

        if nginx -t && systemctl reload nginx; then
            log_success "站点 $domain 已禁用"
        else
            log_error "禁用失败"
            return 1
        fi
    else
        log_error "站点 $domain 未启用或不存在"
        return 1
    fi
}

# ===== 批量操作功能 =====

# 批量SSL证书续期
batch_ssl_renew() {
    log_section "批量SSL证书续期"

    local success_count=0
    local fail_count=0
    local total_count=0

    # 获取所有证书
    if [[ -d "/etc/letsencrypt/live" ]]; then
        for cert_dir in /etc/letsencrypt/live/*/; do
            if [[ -d "$cert_dir" ]]; then
                local domain=$(basename "$cert_dir")
                # 跳过README目录
                [[ "$domain" == "README" ]] && continue

                total_count=$((total_count + 1))
                log_info "处理域名: $domain"

                if ssl_renew "$domain"; then
                    success_count=$((success_count + 1))
                    log_check_success "$domain 续期成功"
                else
                    fail_count=$((fail_count + 1))
                    log_check_error "$domain 续期失败"
                fi
            fi
        done
    fi

    # 显示统计结果
    echo -e "\n${CYAN}📊 批量续期统计：${NC}"
    echo -e "   • 总计: $total_count 个证书"
    echo -e "   • 成功: ${GREEN}$success_count${NC} 个"
    echo -e "   • 失败: ${RED}$fail_count${NC} 个"

    if [[ $fail_count -eq 0 ]]; then
        log_success "所有证书续期完成"
        return 0
    else
        log_warning "部分证书续期失败"
        return 1
    fi
}

# 批量启用站点
batch_enable() {
    local pattern="$1"

    if [[ -z "$pattern" ]]; then
        log_error "缺少匹配模式"
        echo "用法: --batch-enable <pattern>"
        return 1
    fi

    log_section "批量启用站点: $pattern"

    local success_count=0
    local fail_count=0

    # 在conf.d目录中查找匹配的配置文件
    for config_file in "$NGINX_CONF_D"/*"$pattern"*.conf; do
        if [[ -f "$config_file" ]]; then
            local domain=$(basename "$config_file" .conf)
            log_info "启用站点: $domain"

            if enable_site "$domain"; then
                success_count=$((success_count + 1))
                log_check_success "$domain 启用成功"
            else
                fail_count=$((fail_count + 1))
                log_check_error "$domain 启用失败"
            fi
        fi
    done

    # 在sites-available目录中查找
    for config_file in "$NGINX_SITES_AVAILABLE"/*"$pattern"*.conf; do
        if [[ -f "$config_file" ]]; then
            local domain=$(basename "$config_file" .conf)
            log_info "启用站点: $domain"

            if enable_site "$domain"; then
                success_count=$((success_count + 1))
                log_check_success "$domain 启用成功"
            else
                fail_count=$((fail_count + 1))
                log_check_error "$domain 启用失败"
            fi
        fi
    done

    echo -e "\n${CYAN}📊 批量启用统计：${NC}"
    echo -e "   • 成功: ${GREEN}$success_count${NC} 个站点"
    echo -e "   • 失败: ${RED}$fail_count${NC} 个站点"
}

# 批量禁用站点
batch_disable() {
    local pattern="$1"

    if [[ -z "$pattern" ]]; then
        log_error "缺少匹配模式"
        echo "用法: --batch-disable <pattern>"
        return 1
    fi

    log_section "批量禁用站点: $pattern"

    local success_count=0
    local fail_count=0

    # 在启用的站点中查找匹配的配置
    for config_file in "$NGINX_SITES_ENABLED"/*"$pattern"*.conf; do
        if [[ -f "$config_file" || -L "$config_file" ]]; then
            local domain=$(basename "$config_file" .conf)
            log_info "禁用站点: $domain"

            if disable_site "$domain"; then
                success_count=$((success_count + 1))
                log_check_success "$domain 禁用成功"
            else
                fail_count=$((fail_count + 1))
                log_check_error "$domain 禁用失败"
            fi
        fi
    done

    echo -e "\n${CYAN}📊 批量禁用统计：${NC}"
    echo -e "   • 成功: ${GREEN}$success_count${NC} 个站点"
    echo -e "   • 失败: ${RED}$fail_count${NC} 个站点"
}

# 批量备份配置
batch_backup() {
    log_section "批量备份配置"

    local backup_timestamp=$(date +%Y%m%d-%H%M%S)
    local batch_backup_dir="$BACKUP_DIR/batch-backup-$backup_timestamp"

    ensure_directory "$batch_backup_dir" "批量备份目录"

    local success_count=0
    local fail_count=0

    # 备份主配置文件
    if [[ -f "$MAIN_CONFIG_FILE" ]]; then
        if cp "$MAIN_CONFIG_FILE" "$batch_backup_dir/"; then
            success_count=$((success_count + 1))
            log_check_success "主配置文件备份成功"
        else
            fail_count=$((fail_count + 1))
            log_check_error "主配置文件备份失败"
        fi
    fi

    # 备份所有conf.d配置
    if [[ -d "$NGINX_CONF_D" ]]; then
        for config_file in "$NGINX_CONF_D"/*.conf; do
            if [[ -f "$config_file" ]]; then
                local filename=$(basename "$config_file")
                if cp "$config_file" "$batch_backup_dir/"; then
                    success_count=$((success_count + 1))
                    log_check_success "$filename 备份成功"
                else
                    fail_count=$((fail_count + 1))
                    log_check_error "$filename 备份失败"
                fi
            fi
        done
    fi

    # 备份sites-available配置
    if [[ -d "$NGINX_SITES_AVAILABLE" ]]; then
        local sites_backup_dir="$batch_backup_dir/sites-available"
        ensure_directory "$sites_backup_dir" "sites-available备份目录"

        for config_file in "$NGINX_SITES_AVAILABLE"/*.conf; do
            if [[ -f "$config_file" ]]; then
                local filename=$(basename "$config_file")
                if cp "$config_file" "$sites_backup_dir/"; then
                    success_count=$((success_count + 1))
                    log_check_success "sites-available/$filename 备份成功"
                else
                    fail_count=$((fail_count + 1))
                    log_check_error "sites-available/$filename 备份失败"
                fi
            fi
        done
    fi

    # 创建备份清单
    cat > "$batch_backup_dir/backup-manifest.txt" << EOF
# Nginx配置批量备份清单
# 备份时间: $(date)
# 备份目录: $batch_backup_dir

## 备份统计
成功: $success_count 个文件
失败: $fail_count 个文件

## 备份内容
$(find "$batch_backup_dir" -type f -name "*.conf" | sort)
EOF

    echo -e "\n${CYAN}📊 批量备份统计：${NC}"
    echo -e "   • 备份目录: $batch_backup_dir"
    echo -e "   • 成功: ${GREEN}$success_count${NC} 个文件"
    echo -e "   • 失败: ${RED}$fail_count${NC} 个文件"

    if [[ $fail_count -eq 0 ]]; then
        log_success "批量备份完成"
        return 0
    else
        log_warning "部分文件备份失败"
        return 1
    fi
}

# 申请SSL证书
request_ssl_cert() {
    local domain="$1"
    local email="${2:-admin@$domain}"

    if [[ -z "$domain" ]]; then
        log_error "缺少域名参数"
        echo "用法: --ssl-cert <domain> [email]"
        return 1
    fi

    log_section "申请SSL证书: $domain"

    # 检查certbot是否安装
    if ! command -v certbot &> /dev/null; then
        log_info "安装certbot..."
        apt-get update && apt-get install -y certbot python3-certbot-nginx
    fi

    # 检查域名配置是否存在
    local config_file="$NGINX_CONF_D/${domain}.conf"
    local available_file="$NGINX_SITES_AVAILABLE/${domain}.conf"

    if [[ ! -f "$config_file" && ! -f "$available_file" ]]; then
        log_error "域名配置不存在: $domain"
        log_info "请先使用 --add-project 添加项目配置"
        return 1
    fi

    # 确保nginx运行
    if ! systemctl is-active --quiet nginx; then
        log_info "启动nginx服务..."
        systemctl start nginx
    fi

    # 申请证书
    log_info "申请Let's Encrypt证书..."
    if certbot --nginx -d "$domain" --non-interactive --agree-tos --email "$email"; then
        log_success "SSL证书申请成功: $domain"

        # 测试自动续期
        log_info "测试证书自动续期..."
        certbot renew --dry-run

        # 重载nginx
        systemctl reload nginx

        log_success "SSL配置完成，网站现在支持HTTPS访问"
    else
        log_error "SSL证书申请失败"
        return 1
    fi
}

# ===== 配置转换和验证功能 =====

# 转换传统配置为模块化配置
convert_config() {
    local config_file="$1"

    if [[ -z "$config_file" || ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        echo "用法: --convert-config <config_file>"
        return 1
    fi

    log_section "转换配置文件: $config_file"

    local output_dir="$BACKUP_DIR/converted-$(date +%Y%m%d-%H%M%S)"
    ensure_directory "$output_dir" "转换输出目录"

    local converted_file="$output_dir/$(basename "$config_file")"

    # 备份原文件
    cp "$config_file" "$output_dir/original-$(basename "$config_file")"

    # 开始转换
    log_info "分析配置文件结构..."

    # 提取server块
    local server_count=0
    while IFS= read -r line; do
        if [[ "$line" =~ ^[[:space:]]*server[[:space:]]*\{ ]]; then
            server_count=$((server_count + 1))
        fi
    done < "$config_file"

    log_info "发现 $server_count 个server块"

    # 创建转换后的配置
    cat > "$converted_file" << 'EOF'
# 转换后的模块化nginx配置
# 原文件: CONFIG_FILE_PLACEHOLDER
# 转换时间: TIMESTAMP_PLACEHOLDER

# 包含通用配置片段
include SNIPPETS_DIR_PLACEHOLDER/ssl-params.conf;
include SNIPPETS_DIR_PLACEHOLDER/security-headers.conf;
include SNIPPETS_DIR_PLACEHOLDER/performance.conf;

EOF

    # 替换占位符
    sed -i "s|CONFIG_FILE_PLACEHOLDER|$config_file|g" "$converted_file"
    sed -i "s|TIMESTAMP_PLACEHOLDER|$(date)|g" "$converted_file"
    sed -i "s|SNIPPETS_DIR_PLACEHOLDER|$SNIPPETS_DIR|g" "$converted_file"

    # 处理原配置文件内容
    local in_server_block=false
    local brace_count=0
    local current_server=""

    while IFS= read -r line; do
        # 检测server块开始
        if [[ "$line" =~ ^[[:space:]]*server[[:space:]]*\{ ]]; then
            in_server_block=true
            brace_count=1
            current_server="$line"
            continue
        fi

        if [[ "$in_server_block" == "true" ]]; then
            current_server+=$'\n'"$line"

            # 计算大括号
            local open_braces=$(echo "$line" | tr -cd '{' | wc -c)
            local close_braces=$(echo "$line" | tr -cd '}' | wc -c)
            brace_count=$((brace_count + open_braces - close_braces))

            # server块结束
            if [[ $brace_count -eq 0 ]]; then
                # 处理这个server块
                echo "" >> "$converted_file"
                echo "# 转换的server块" >> "$converted_file"
                echo "$current_server" >> "$converted_file"

                in_server_block=false
                current_server=""
            fi
        else
            # 非server块内容，检查是否是有用的配置
            if [[ "$line" =~ ^[[:space:]]*upstream[[:space:]] ]] ||
               [[ "$line" =~ ^[[:space:]]*map[[:space:]] ]] ||
               [[ "$line" =~ ^[[:space:]]*limit_req_zone[[:space:]] ]]; then
                echo "$line" >> "$converted_file"
            fi
        fi
    done < "$config_file"

    # 验证转换后的配置
    log_info "验证转换后的配置..."
    if nginx -t -c "$converted_file" 2>/dev/null; then
        log_success "配置转换成功: $converted_file"
        log_info "原文件备份: $output_dir/original-$(basename "$config_file")"
    else
        log_warning "转换后的配置语法有问题，需要手动调整"
        log_info "转换文件: $converted_file"
    fi

    # 生成转换报告
    cat > "$output_dir/conversion-report.txt" << EOF
# Nginx配置转换报告
转换时间: $(date)
原文件: $config_file
转换文件: $converted_file

## 转换统计
Server块数量: $server_count
输出目录: $output_dir

## 建议的后续步骤
1. 检查转换后的配置文件
2. 根据需要调整server块配置
3. 使用模块化snippets替换重复配置
4. 测试配置语法: nginx -t -c $converted_file
5. 应用配置: cp $converted_file /etc/nginx/conf.d/

## 注意事项
- 转换是自动化的，可能需要手动调整
- 建议使用snippets替换重复的配置块
- 检查SSL证书路径和upstream配置
EOF

    log_info "转换报告: $output_dir/conversion-report.txt"
}

# 验证配置文件
validate_config() {
    local config_file="$1"

    log_section "验证nginx配置"

    if [[ -n "$config_file" ]]; then
        # 验证指定文件
        if [[ ! -f "$config_file" ]]; then
            log_error "配置文件不存在: $config_file"
            return 1
        fi

        log_info "验证配置文件: $config_file"
        if nginx -t -c "$config_file"; then
            log_success "配置文件语法正确"
        else
            log_error "配置文件语法错误"
            return 1
        fi
    else
        # 验证当前nginx配置
        log_info "验证当前nginx配置..."
        if nginx -t; then
            log_success "当前nginx配置语法正确"
        else
            log_error "当前nginx配置语法错误"
            return 1
        fi
    fi

    # 额外的配置检查
    log_info "执行额外的配置检查..."

    local warnings=0

    # 检查常见问题
    if [[ -n "$config_file" ]]; then
        local check_file="$config_file"
    else
        local check_file="/etc/nginx/nginx.conf"
    fi

    # 检查server_tokens
    if ! grep -q "server_tokens off" "$check_file" 2>/dev/null; then
        log_check_warning "建议设置 server_tokens off 隐藏nginx版本"
        warnings=$((warnings + 1))
    fi

    # 检查SSL配置
    if grep -q "ssl_certificate" "$check_file" 2>/dev/null; then
        if ! grep -q "ssl_protocols" "$check_file" 2>/dev/null; then
            log_check_warning "建议明确设置 ssl_protocols"
            warnings=$((warnings + 1))
        fi
    fi

    # 检查gzip配置
    if ! grep -q "gzip on" "$check_file" 2>/dev/null; then
        log_check_warning "建议启用gzip压缩"
        warnings=$((warnings + 1))
    fi

    if [[ $warnings -eq 0 ]]; then
        log_success "配置检查完成，未发现问题"
    else
        log_info "配置检查完成，发现 $warnings 个建议优化项"
    fi
}

# 分析配置性能
analyze_performance() {
    log_section "nginx配置性能分析"

    # 检查worker进程配置
    local worker_processes=$(nginx -T 2>/dev/null | grep "worker_processes" | head -1 | awk '{print $2}' | tr -d ';')
    local cpu_cores=$(nproc)

    echo -e "${CYAN}📊 进程配置分析：${NC}"
    echo -e "   • CPU核心数: $cpu_cores"
    echo -e "   • Worker进程数: $worker_processes"

    if [[ "$worker_processes" == "auto" ]]; then
        log_check_success "Worker进程配置为auto，将自动匹配CPU核心数"
    elif [[ "$worker_processes" -eq "$cpu_cores" ]]; then
        log_check_success "Worker进程数与CPU核心数匹配"
    else
        log_check_warning "建议将worker_processes设置为auto或$cpu_cores"
    fi

    # 检查连接数配置
    local worker_connections=$(nginx -T 2>/dev/null | grep "worker_connections" | head -1 | awk '{print $2}' | tr -d ';')
    echo -e "   • Worker连接数: $worker_connections"

    if [[ "$worker_connections" -ge 1024 ]]; then
        log_check_success "Worker连接数配置合理"
    else
        log_check_warning "建议增加worker_connections到1024或更高"
    fi

    # 检查缓冲区配置
    echo -e "\n${CYAN}📊 缓冲区配置分析：${NC}"

    local client_max_body_size=$(nginx -T 2>/dev/null | grep "client_max_body_size" | head -1 | awk '{print $2}' | tr -d ';')
    echo -e "   • 客户端最大请求体: ${client_max_body_size:-默认1m}"

    local client_body_buffer_size=$(nginx -T 2>/dev/null | grep "client_body_buffer_size" | head -1 | awk '{print $2}' | tr -d ';')
    echo -e "   • 客户端请求体缓冲: ${client_body_buffer_size:-默认8k/16k}"

    # 检查gzip配置
    echo -e "\n${CYAN}📊 压缩配置分析：${NC}"

    if nginx -T 2>/dev/null | grep -q "gzip on"; then
        log_check_success "gzip压缩已启用"

        local gzip_comp_level=$(nginx -T 2>/dev/null | grep "gzip_comp_level" | head -1 | awk '{print $2}' | tr -d ';')
        echo -e "   • 压缩级别: ${gzip_comp_level:-默认1}"

        if [[ -n "$gzip_comp_level" && "$gzip_comp_level" =~ ^[0-9]+$ ]]; then
            if [[ "$gzip_comp_level" -ge 4 && "$gzip_comp_level" -le 6 ]]; then
                log_check_success "gzip压缩级别配置合理"
            else
                log_check_warning "建议设置gzip_comp_level为4-6之间"
            fi
        else
            log_check_warning "建议设置gzip_comp_level为4-6之间"
        fi
    else
        log_check_warning "建议启用gzip压缩"
    fi

    # 检查keepalive配置
    echo -e "\n${CYAN}📊 连接保持配置分析：${NC}"

    local keepalive_timeout=$(nginx -T 2>/dev/null | grep "keepalive_timeout" | head -1 | awk '{print $2}' | tr -d ';')
    echo -e "   • Keepalive超时: ${keepalive_timeout:-默认75s}"

    if [[ -n "$keepalive_timeout" ]]; then
        local timeout_value=$(echo "$keepalive_timeout" | sed 's/s$//')
        if [[ "$timeout_value" -ge 30 && "$timeout_value" -le 75 ]]; then
            log_check_success "Keepalive超时配置合理"
        else
            log_check_warning "建议设置keepalive_timeout为30-75秒"
        fi
    fi

    # 性能建议
    echo -e "\n${CYAN}💡 性能优化建议：${NC}"
    echo -e "   • 使用HTTP/2: 在listen指令中添加http2"
    echo -e "   • 启用文件缓存: 配置open_file_cache"
    echo -e "   • 优化日志: 使用buffer和flush参数"
    echo -e "   • 启用sendfile: 提高文件传输效率"
    echo -e "   • 配置upstream keepalive: 减少后端连接开销"
}

# 安全配置检查
check_security() {
    log_section "nginx安全配置检查"

    local security_score=0
    local total_checks=10

    echo -e "${CYAN}🔒 安全配置检查：${NC}"

    # 检查server_tokens
    if nginx -T 2>/dev/null | grep -q "server_tokens off"; then
        log_check_success "已隐藏nginx版本信息"
        security_score=$((security_score + 1))
    else
        log_check_error "未隐藏nginx版本信息 (server_tokens off)"
    fi

    # 检查SSL配置
    if nginx -T 2>/dev/null | grep -q "ssl_protocols"; then
        log_check_success "已配置SSL协议"
        security_score=$((security_score + 1))

        # 检查是否禁用了不安全的协议
        if nginx -T 2>/dev/null | grep "ssl_protocols" | grep -v -E "(SSLv2|SSLv3|TLSv1\.0|TLSv1\.1)"; then
            log_check_success "SSL协议配置安全"
            security_score=$((security_score + 1))
        else
            log_check_warning "建议禁用不安全的SSL协议"
        fi
    else
        log_check_warning "未配置SSL协议"
    fi

    # 检查安全头
    local security_headers=("X-Frame-Options" "X-Content-Type-Options" "X-XSS-Protection" "Strict-Transport-Security")
    local headers_found=0

    for header in "${security_headers[@]}"; do
        if nginx -T 2>/dev/null | grep -q "$header"; then
            headers_found=$((headers_found + 1))
        fi
    done

    if [[ $headers_found -ge 3 ]]; then
        log_check_success "安全头配置良好 ($headers_found/4)"
        security_score=$((security_score + 2))
    elif [[ $headers_found -ge 1 ]]; then
        log_check_warning "部分安全头已配置 ($headers_found/4)"
        security_score=$((security_score + 1))
    else
        log_check_error "缺少安全头配置"
    fi

    # 检查访问日志
    if nginx -T 2>/dev/null | grep -q "access_log.*off"; then
        log_check_warning "访问日志已关闭，可能影响安全监控"
    else
        log_check_success "访问日志已启用"
        security_score=$((security_score + 1))
    fi

    # 检查错误页面
    if nginx -T 2>/dev/null | grep -q "error_page"; then
        log_check_success "已配置自定义错误页面"
        security_score=$((security_score + 1))
    else
        log_check_warning "建议配置自定义错误页面"
    fi

    # 检查rate limiting
    if nginx -T 2>/dev/null | grep -q "limit_req"; then
        log_check_success "已配置请求限制"
        security_score=$((security_score + 1))
    else
        log_check_warning "建议配置请求限制防止DDoS"
    fi

    # 检查文件上传限制
    if nginx -T 2>/dev/null | grep -q "client_max_body_size"; then
        log_check_success "已配置文件上传大小限制"
        security_score=$((security_score + 1))
    else
        log_check_warning "建议配置client_max_body_size限制"
    fi

    # 安全评分
    echo -e "\n${CYAN}📊 安全评分：${NC}"
    local percentage=$((security_score * 100 / total_checks))

    if [[ $percentage -ge 80 ]]; then
        echo -e "   • 评分: ${GREEN}$security_score/$total_checks ($percentage%)${NC} - 优秀"
    elif [[ $percentage -ge 60 ]]; then
        echo -e "   • 评分: ${YELLOW}$security_score/$total_checks ($percentage%)${NC} - 良好"
    else
        echo -e "   • 评分: ${RED}$security_score/$total_checks ($percentage%)${NC} - 需要改进"
    fi

    # 安全建议
    echo -e "\n${CYAN}💡 安全建议：${NC}"
    echo -e "   • 定期更新nginx版本"
    echo -e "   • 使用强SSL密码套件"
    echo -e "   • 配置防火墙规则"
    echo -e "   • 启用访问日志监控"
    echo -e "   • 定期检查安全漏洞"
}

# 从模板创建配置
create_from_template() {
    local template="$1"
    local project_name="$2"
    local domain="$3"
    local backend_host="${4:-127.0.0.1}"
    local backend_port="${5:-3000}"
    local location_path="${6:-/}"
    local project_root="${7:-/var/www/$project_name}"

    # 参数验证
    if [[ -z "$template" || -z "$project_name" || -z "$domain" ]]; then
        log_error "缺少必需参数"
        echo "用法: --create-from-template <template> <project_name> <domain> [backend_host] [backend_port] [location_path] [project_root]"
        return 1
    fi

    log_section "从模板创建配置: $template"

    # 检查模板是否存在
    local template_file="$TEMPLATES_DIR/${template}.tpl"
    if [[ ! -f "$template_file" ]]; then
        log_error "模板不存在: $template"
        list_templates
        return 1
    fi

    # 生成配置文件
    local config_file="$NGINX_CONF_D/${domain}.conf"
    log_info "生成配置文件: $config_file"

    # 复制模板并替换占位符
    cp "$template_file" "$config_file"

    # 替换所有占位符
    sed -i "s/__DOMAIN__/$domain/g" "$config_file"
    sed -i "s/__PROJECT_NAME__/$project_name/g" "$config_file"
    sed -i "s/__BACKEND_HOST__/$backend_host/g" "$config_file"
    sed -i "s/__BACKEND_PORT__/$backend_port/g" "$config_file"
    sed -i "s|__LOCATION_PATH__|$location_path|g" "$config_file"
    sed -i "s|__PROJECT_ROOT__|$project_root|g" "$config_file"

    # 为测试环境注释掉SSL相关配置
    if [[ ! -d "/etc/letsencrypt/live/$domain" ]]; then
        log_info "SSL证书不存在，生成HTTP-only配置"
        sed -i 's|listen 443 ssl|listen 443|g' "$config_file"
        sed -i 's|ssl_certificate |# ssl_certificate |g' "$config_file"
        sed -i 's|ssl_certificate_key |# ssl_certificate_key |g' "$config_file"
        sed -i 's|include.*ssl-params.conf|# include ssl-params.conf (SSL disabled)|g' "$config_file"
    fi

    # 创建项目目录
    if [[ ! -d "$project_root" ]]; then
        log_info "创建项目目录: $project_root"
        mkdir -p "$project_root"
        chown -R www-data:www-data "$project_root" 2>/dev/null || true
    fi

    # 测试配置
    if nginx -t; then
        log_success "配置文件生成成功: $config_file"

        # 显示配置信息
        echo -e "\n${CYAN}📋 配置信息：${NC}"
        echo -e "   • 模板: $template"
        echo -e "   • 项目名: $project_name"
        echo -e "   • 域名: $domain"
        echo -e "   • 后端: $backend_host:$backend_port"
        echo -e "   • 路径: $location_path"
        echo -e "   • 根目录: $project_root"
        echo -e "   • 配置文件: $config_file"

        log_info "请运行以下命令应用配置:"
        echo -e "   ${GREEN}$0 --apply${NC}"
        echo -e "   ${GREEN}$0 --ssl-cert $domain${NC}  # 申请SSL证书"

    else
        log_error "配置语法错误，删除生成的文件"
        rm -f "$config_file"
        return 1
    fi
}

# 主函数
main() {
    print_banner
    
    # 检查权限
    check_root
    
    # 默认参数
    GENERATE_CONFIG=false
    APPLY_CONFIG=false
    TEST_CONFIG=false
    BACKUP_CONFIG=false
    RESTORE_CONFIG=false
    SHOW_STATUS=false
    CHECK_CONFLICTS=false
    CLEAN_CONFLICTS=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --generate)
                GENERATE_CONFIG=true
                shift
                ;;
            --apply)
                APPLY_CONFIG=true
                shift
                ;;
            --test)
                TEST_CONFIG=true
                shift
                ;;
            --backup)
                BACKUP_CONFIG=true
                shift
                ;;
            --restore)
                RESTORE_CONFIG=true
                shift
                ;;
            --status)
                SHOW_STATUS=true
                shift
                ;;
            --conflicts)
                CHECK_CONFLICTS=true
                shift
                ;;
            --clean)
                CLEAN_CONFLICTS=true
                shift
                ;;
            --add-project)
                if [[ $# -lt 4 ]]; then
                    log_error "add-project需要至少3个参数: domain project_name template"
                    exit 1
                fi
                add_project "$2" "$3" "$4" "$5" "$6" "$7"
                exit $?
                ;;
            --enable-site)
                if [[ -z "$2" ]]; then
                    log_error "enable-site需要域名参数"
                    exit 1
                fi
                enable_site "$2"
                exit $?
                ;;
            --disable-site)
                if [[ -z "$2" ]]; then
                    log_error "disable-site需要域名参数"
                    exit 1
                fi
                disable_site "$2"
                exit $?
                ;;
            --list-templates)
                list_templates
                exit $?
                ;;
            --ssl-cert)
                if [[ -z "$2" ]]; then
                    log_error "ssl-cert需要域名参数"
                    exit 1
                fi
                request_ssl_cert "$2" "$3"
                exit $?
                ;;
            --ssl-renew)
                ssl_renew "$2"
                exit $?
                ;;
            --ssl-status)
                ssl_status "$2"
                exit $?
                ;;
            --ssl-auto-renew)
                ssl_auto_renew
                exit $?
                ;;
            --batch-ssl-renew)
                batch_ssl_renew
                exit $?
                ;;
            --batch-enable)
                if [[ -z "$2" ]]; then
                    log_error "batch-enable需要匹配模式参数"
                    exit 1
                fi
                batch_enable "$2"
                exit $?
                ;;
            --batch-disable)
                if [[ -z "$2" ]]; then
                    log_error "batch-disable需要匹配模式参数"
                    exit 1
                fi
                batch_disable "$2"
                exit $?
                ;;
            --batch-backup)
                batch_backup
                exit $?
                ;;
            --convert-config)
                if [[ -z "$2" ]]; then
                    log_error "convert-config需要配置文件参数"
                    exit 1
                fi
                convert_config "$2"
                exit $?
                ;;
            --validate-config)
                validate_config "$2"
                exit $?
                ;;
            --analyze-performance)
                analyze_performance
                exit $?
                ;;
            --check-security)
                check_security
                exit $?
                ;;
            --create-from-template)
                if [[ $# -lt 4 ]]; then
                    log_error "create-from-template需要至少3个参数: template project_name domain"
                    exit 1
                fi
                create_from_template "$2" "$3" "$4" "$5" "$6" "$7" "$8"
                exit $?
                ;;
            --help|-h)
                show_enhanced_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定任何选项，显示状态
    if [[ "$GENERATE_CONFIG" == "false" && "$APPLY_CONFIG" == "false" && 
          "$TEST_CONFIG" == "false" && "$BACKUP_CONFIG" == "false" && 
          "$RESTORE_CONFIG" == "false" && "$SHOW_STATUS" == "false" && 
          "$CHECK_CONFLICTS" == "false" && "$CLEAN_CONFLICTS" == "false" ]]; then
        SHOW_STATUS=true
    fi
    
    # 显示环境信息
    show_environment_info
    
    # 执行操作
    [[ "$CHECK_CONFLICTS" == "true" ]] && check_conflicts
    [[ "$CLEAN_CONFLICTS" == "true" ]] && clean_conflicts
    [[ "$BACKUP_CONFIG" == "true" ]] && backup_file "$MAIN_CONFIG_FILE"
    [[ "$GENERATE_CONFIG" == "true" ]] && generate_unified_config
    [[ "$TEST_CONFIG" == "true" ]] && nginx -t
    [[ "$APPLY_CONFIG" == "true" ]] && apply_config
    [[ "$SHOW_STATUS" == "true" ]] && show_status
    
    log_success "Nginx配置管理完成"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
