networks:
  new-api-network:
    driver: bridge

services:
  new-api:
    image: calciumion/new-api:latest
    container_name: new-api
    restart: always
    command: --log-dir /app/logs
    # Remove external port exposure since nginx will handle it
    expose:
      - "3000"
    volumes:
      - ./data:/data
      - ./logs:/app/logs
    env_file:
      - .env
    environment:
      # Override specific environment variables if needed
      - TZ=Asia/Shanghai
    depends_on:
      redis:
        condition: service_started
      mysql:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "wget -q -O - http://localhost:3000/api/status | grep -o '\"success\":\\s*true' | awk -F: '{print $$2}'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - new-api-network

  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    restart: always
    # Map to port 8080 for system nginx to proxy
    ports:
      - "8080:80"
    volumes:
      - ./nginx-newapi/default.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
      # Optional: Mount static files for /love path
      - ./static/love:/var/www/love:ro
      # Mount AI frontend files
      - ./web/dist:/var/www/ai:ro
    depends_on:
      new-api:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - new-api-network

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: always
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
      - ./logs/redis:/var/log/redis
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - new-api-network

  mysql:
    image: mysql:8.2
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-NewAPI_2024_SecurePass_Prod}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-new-api}
      MYSQL_USER: newapi
      MYSQL_PASSWORD: ${MYSQL_ROOT_PASSWORD:-NewAPI_2024_SecurePass_Prod}
      TZ: Asia/Shanghai
    volumes:
      - mysql_data:/var/lib/mysql
      - ./logs/mysql:/var/log/mysql
      - ./backup:/docker-entrypoint-initdb.d:ro
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=256M
      --max-connections=200
      --log-error=/var/log/mysql/error.log
      --slow-query-log=1
      --slow-query-log-file=/var/log/mysql/slow.log
      --long-query-time=2
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-NewAPI_2024_SecurePass_Prod}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - new-api-network
    # Uncomment if you need external access
    # ports:
    #   - "3306:3306"

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
