[SYS] 2025/07/26 - 13:57:37 | initializing token encoders 
[SYS] 2025/07/26 - 13:57:37 | token encoders initialized 
[SYS] 2025/07/26 - 13:57:37 | using MySQL as database 
[FATAL] 2025/07/26 - 13:57:37 | [dial tcp 172.19.0.3:3306: connect: connection refused] 
[SYS] 2025/07/26 - 13:57:37 | initializing token encoders 
[SYS] 2025/07/26 - 13:57:37 | token encoders initialized 
[SYS] 2025/07/26 - 13:57:37 | using MySQL as database 
[SYS] 2025/07/26 - 13:57:37 | database migration started 
[SYS] 2025/07/26 - 13:57:38 | database migrated 
[SYS] 2025/07/26 - 13:57:38 | system is already initialized at: 2025-06-29 19:19:27 +0800 CST 
[SYS] 2025/07/26 - 13:57:38 | Redis is enabled 
[SYS] 2025/07/26 - 13:57:38 | New API v0.8.8.0-alpha.5 started 
[SYS] 2025/07/26 - 13:57:38 | memory cache enabled 
[SYS] 2025/07/26 - 13:57:38 | sync frequency: 60 seconds 
[SYS] 2025/07/26 - 13:57:38 | channels synced from database 
[SYS] 2025/07/26 - 13:57:38 | batch update enabled with interval 5s 
[SYS] 2025/07/26 - 13:57:38 | FRONTEND_BASE_URL is ignored on master node 
[SYS] 2025/07/26 - 13:57:38 | 正在更新数据看板数据... 
[SYS] 2025/07/26 - 13:57:38 | 保存数据看板数据成功，共保存0条数据 
[GIN] 2025/07/26 - 13:57:42 | 20250726135742905823575x7gqW0v5 | 200 |    2.707352ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 13:57:53 | 任务进度轮询开始 
[SYS] 2025/07/26 - 13:57:53 | 任务进度轮询完成 
[SYS] 2025/07/26 - 13:58:08 | 任务进度轮询开始 
[SYS] 2025/07/26 - 13:58:08 | 任务进度轮询完成 
[GIN] 2025/07/26 - 13:58:13 | 20250726135812991963082sKM0ALbB | 200 |   44.928257ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 13:58:23 | 任务进度轮询开始 
[SYS] 2025/07/26 - 13:58:23 | 任务进度轮询完成 
[SYS] 2025/07/26 - 13:58:38 | syncing options from database 
[SYS] 2025/07/26 - 13:58:38 | syncing channels from database 
[SYS] 2025/07/26 - 13:58:38 | channels synced from database 
[SYS] 2025/07/26 - 13:58:38 | 任务进度轮询开始 
[SYS] 2025/07/26 - 13:58:38 | 任务进度轮询完成 
[GIN] 2025/07/26 - 13:58:43 | 20250726135843107805102f9vtpWn7 | 200 |   16.990638ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 13:58:53 | 任务进度轮询开始 
[SYS] 2025/07/26 - 13:58:53 | 任务进度轮询完成 
[SYS] 2025/07/26 - 13:59:08 | 任务进度轮询开始 
[SYS] 2025/07/26 - 13:59:08 | 任务进度轮询完成 
[GIN] 2025/07/26 - 13:59:13 | 20250726135913189104776RfFPcKE8 | 200 |   21.955822ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 13:59:23 | 任务进度轮询开始 
[SYS] 2025/07/26 - 13:59:23 | 任务进度轮询完成 
[SYS] 2025/07/26 - 13:59:38 | syncing options from database 
[SYS] 2025/07/26 - 13:59:38 | syncing channels from database 
[SYS] 2025/07/26 - 13:59:38 | 任务进度轮询开始 
[SYS] 2025/07/26 - 13:59:38 | 任务进度轮询完成 
[SYS] 2025/07/26 - 13:59:38 | channels synced from database 
[GIN] 2025/07/26 - 13:59:43 | 20250726135943266202176PIZSZsjF | 200 |    6.919776ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 13:59:53 | 任务进度轮询开始 
[SYS] 2025/07/26 - 13:59:53 | 任务进度轮询完成 
[SYS] 2025/07/26 - 14:00:08 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:00:08 | 任务进度轮询完成 
[GIN] 2025/07/26 - 14:00:13 | 20250726140013365590207edcan9db | 200 |    1.935112ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 14:00:23 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:00:23 | 任务进度轮询完成 
[SYS] 2025/07/26 - 14:00:38 | syncing options from database 
[SYS] 2025/07/26 - 14:00:38 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:00:38 | 任务进度轮询完成 
[SYS] 2025/07/26 - 14:00:38 | syncing channels from database 
[SYS] 2025/07/26 - 14:00:38 | channels synced from database 
[GIN] 2025/07/26 - 14:00:43 | 20250726140043454918325yZcJp6bF | 200 |   16.556796ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 14:00:53 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:00:53 | 任务进度轮询完成 
