[SYS] 2025/07/26 - 14:03:40 | initializing token encoders 
[SYS] 2025/07/26 - 14:03:40 | token encoders initialized 
[SYS] 2025/07/26 - 14:03:40 | using MySQL as database 
[SYS] 2025/07/26 - 14:03:43 | database migration started 
[SYS] 2025/07/26 - 14:03:43 | database migrated 
[SYS] 2025/07/26 - 14:03:43 | system is already initialized at: 2025-06-29 19:19:27 +0800 CST 
[SYS] 2025/07/26 - 14:03:43 | Redis is enabled 
[SYS] 2025/07/26 - 14:03:43 | New API v0.8.8.0-alpha.5 started 
[SYS] 2025/07/26 - 14:03:43 | memory cache enabled 
[SYS] 2025/07/26 - 14:03:43 | sync frequency: 60 seconds 
[SYS] 2025/07/26 - 14:03:43 | channels synced from database 
[SYS] 2025/07/26 - 14:03:43 | batch update enabled with interval 5s 
[SYS] 2025/07/26 - 14:03:43 | 正在更新数据看板数据... 
[SYS] 2025/07/26 - 14:03:43 | 保存数据看板数据成功，共保存0条数据 
[SYS] 2025/07/26 - 14:03:43 | FRONTEND_BASE_URL is ignored on master node 
[GIN] 2025/07/26 - 14:03:45 | 20250726140345257597021wdA6FL55 | 200 |     813.628µs |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 14:03:58 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:03:58 | 任务进度轮询完成 
[SYS] 2025/07/26 - 14:04:14 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:04:14 | 任务进度轮询完成 
[GIN] 2025/07/26 - 14:04:15 | 20250726140415317330886GgLzmL0t | 200 |   28.724538ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 14:04:29 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:04:29 | 任务进度轮询完成 
[SYS] 2025/07/26 - 14:04:43 | syncing channels from database 
[SYS] 2025/07/26 - 14:04:43 | syncing options from database 
[SYS] 2025/07/26 - 14:04:44 | channels synced from database 
[SYS] 2025/07/26 - 14:04:44 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:04:44 | 任务进度轮询完成 
[GIN] 2025/07/26 - 14:04:45 | 20250726140445412387163vbG02SLX | 200 |   16.471669ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 14:04:59 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:04:59 | 任务进度轮询完成 
[SYS] 2025/07/26 - 14:05:14 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:05:14 | 任务进度轮询完成 
[GIN] 2025/07/26 - 14:05:15 | 20250726140515497717361Pmiw9aXn | 200 |    1.626105ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 14:05:29 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:05:29 | 任务进度轮询完成 
[SYS] 2025/07/26 - 14:05:44 | syncing options from database 
[SYS] 2025/07/26 - 14:05:44 | syncing channels from database 
[SYS] 2025/07/26 - 14:05:44 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:05:44 | channels synced from database 
[SYS] 2025/07/26 - 14:05:44 | 任务进度轮询完成 
[GIN] 2025/07/26 - 14:05:45 | 20250726140545579860949NhzAdPwq | 200 |   19.610374ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 14:05:59 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:05:59 | 任务进度轮询完成 
[SYS] 2025/07/26 - 14:06:14 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:06:14 | 任务进度轮询完成 
[GIN] 2025/07/26 - 14:06:15 | 20250726140615678173431Fe2yV71a | 200 |   21.345956ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 14:06:29 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:06:29 | 任务进度轮询完成 
[SYS] 2025/07/26 - 14:06:44 | syncing options from database 
[SYS] 2025/07/26 - 14:06:44 | syncing channels from database 
[SYS] 2025/07/26 - 14:06:44 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:06:44 | channels synced from database 
[SYS] 2025/07/26 - 14:06:44 | 任务进度轮询完成 
[GIN] 2025/07/26 - 14:06:45 | 20250726140645775620706RU7p22qf | 200 |   20.835595ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 14:06:59 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:06:59 | 任务进度轮询完成 
[GIN] 2025/07/26 - 14:07:12 | 20250726140712967830328aYaK1BPG | 200 |   14.912752ms | 165.154.112.176 |     GET /ai/console/log
[GIN] 2025/07/26 - 14:07:13 | 20250726140713657802081Evj6iRDM | 200 |     896.193µs | 165.154.112.176 |     GET /api/status
[SYS] 2025/07/26 - 14:07:14 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:07:14 | 任务进度轮询完成 
[GIN] 2025/07/26 - 14:07:15 | 20250726140715883344221Wif4DjeN | 200 |   16.671774ms |             ::1 |     GET /api/status
[GIN] 2025/07/26 - 14:07:17 | 20250726140717107602270oP6NzIxV | 200 |    5.693007ms | 165.154.112.176 |     GET /api/user/self
[GIN] 2025/07/26 - 14:07:17 | 20250726140717107585940vHfgZpXZ | 200 |    6.800693ms | 165.154.112.176 |     GET /api/data/?username=&start_timestamp=1753423638&end_timestamp=1753513638&default_time=hour
[GIN] 2025/07/26 - 14:07:17 | 202507261407173941058228N9hpbP7 | 200 |     774.579µs | 165.154.112.176 |     GET /api/uptime/status
[GIN] 2025/07/26 - 14:07:18 | 20250726140718400825815RkzZ1g88 | 200 |   35.517787ms | 165.154.112.176 |     GET /api/pricing
[GIN] 2025/07/26 - 14:07:19 | 20250726140719791861667yI0WI0j2 | 200 |    2.426621ms | 165.154.112.176 |     GET /api/user/self
[GIN] 2025/07/26 - 14:07:19 | 20250726140719791892717zykf0k1p | 200 |    3.983536ms | 165.154.112.176 |     GET /api/data/?username=&start_timestamp=1753423640&end_timestamp=1753513640&default_time=hour
[GIN] 2025/07/26 - 14:07:20 | 2025072614072080760503TKuPLySM | 200 |    1.577838ms | 165.154.112.176 |     GET /api/uptime/status
[GIN] 2025/07/26 - 14:07:20 | 202507261407208252970002WCR3q4P | 200 |    3.788573ms | 165.154.112.176 |     GET /api/user/self/groups
[GIN] 2025/07/26 - 14:07:20 | 20250726140720825114820bs2kdx9M | 200 |    5.493335ms | 165.154.112.176 |     GET /api/token/?p=1&size=10
[GIN] 2025/07/26 - 14:07:20 | 20250726140720824659800WEw8n2Cj | 200 |   12.071136ms | 165.154.112.176 |     GET /api/user/models
[GIN] 2025/07/26 - 14:07:21 | 20250726140721818241811P6JIyEJI | 200 |    8.830168ms | 165.154.112.176 |     GET /api/log/?p=1&page_size=100&type=0&username=&token_name=&model_name=&start_timestamp=1753459200&end_timestamp=1753513642&channel=&group=
[GIN] 2025/07/26 - 14:07:21 | 202507261407218450808535czXZaaM | 200 |     3.39274ms | 165.154.112.176 |     GET /api/log/stat?type=0&username=&token_name=&model_name=&start_timestamp=1753459200&end_timestamp=1753513642&channel=&group=
[GIN] 2025/07/26 - 14:07:23 | 20250726140723244610827UjnVRvT4 | 200 |    3.938605ms | 165.154.112.176 |     GET /api/user/self/groups
[GIN] 2025/07/26 - 14:07:23 | 20250726140723244395133fdVKsoB2 | 200 |   16.285955ms | 165.154.112.176 |     GET /api/user/models
[GIN] 2025/07/26 - 14:07:23 | 20250726140723244151713PFtBuZmv | 200 |   17.905093ms | 165.154.112.176 |     GET /api/token/?p=1&size=10
[GIN] 2025/07/26 - 14:07:25 | 20250726140725324282376oe6vRR9U | 200 |    7.447149ms | 165.154.112.176 |     GET /api/log/?p=1&page_size=100&type=0&username=&token_name=&model_name=&start_timestamp=1753459200&end_timestamp=1753513646&channel=&group=
[GIN] 2025/07/26 - 14:07:25 | 20250726140725343322910wvZqBZ3C | 200 |    3.003183ms | 165.154.112.176 |     GET /api/log/stat?type=0&username=&token_name=&model_name=&start_timestamp=1753459200&end_timestamp=1753513646&channel=&group=
[SYS] 2025/07/26 - 14:07:29 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:07:29 | 任务进度轮询完成 
[GIN] 2025/07/26 - 14:07:31 | 20250726140731544575596htxKWXn0 | 200 |   33.046327ms | 165.154.112.176 |     GET /api/mj/?p=1&page_size=10&channel_id=&mj_id=&start_timestamp=1750918052000&end_timestamp=1753513652000
[GIN] 2025/07/26 - 14:07:32 | 20250726140732414231835zD3ATEoh | 200 |    5.227894ms | 165.154.112.176 |     GET /api/log/?p=1&page_size=100&type=0&username=&token_name=&model_name=&start_timestamp=1753459200&end_timestamp=1753513653&channel=&group=
[GIN] 2025/07/26 - 14:07:32 | 202507261407324325076175Er0zrHx | 200 |    2.191278ms | 165.154.112.176 |     GET /api/log/stat?type=0&username=&token_name=&model_name=&start_timestamp=1753459200&end_timestamp=1753513653&channel=&group=
[SYS] 2025/07/26 - 14:07:44 | syncing options from database 
[SYS] 2025/07/26 - 14:07:44 | syncing channels from database 
[SYS] 2025/07/26 - 14:07:44 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:07:44 | channels synced from database 
[SYS] 2025/07/26 - 14:07:44 | 任务进度轮询完成 
[GIN] 2025/07/26 - 14:07:45 | 20250726140745960171171eCXqKJo2 | 200 |    1.713887ms |             ::1 |     GET /api/status
[SYS] 2025/07/26 - 14:07:59 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:07:59 | 任务进度轮询完成 
[SYS] 2025/07/26 - 14:08:14 | 任务进度轮询开始 
[SYS] 2025/07/26 - 14:08:14 | 任务进度轮询完成 
