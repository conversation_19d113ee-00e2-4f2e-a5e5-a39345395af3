#!/bin/bash

# New-API Unified Management Script
# 统一管理脚本 - 整合所有常用功能

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Print functions
print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    New-API 统一管理工具                      ║"
    echo "║                                                              ║"
    echo "║  🚀 服务管理 | 📦 数据备份 | 🔄 项目更新 | ⚙️ 系统配置      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}\n"
}

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# Check if in correct directory
check_directory() {
    if [ ! -f "docker-compose.yml" ] || [ ! -f "guide-newapi.md" ]; then
        print_error "请在new-api项目根目录运行此脚本"
        exit 1
    fi
}

# Check for port conflicts
check_port_conflicts() {
    print_info "检查端口冲突..."

    # Check if port 80 is occupied by Docker (should not be)
    if netstat -tlnp | grep ":80" | grep -q "docker-proxy"; then
        print_warning "检测到Docker容器占用80端口，这可能导致系统nginx无法启动"
        print_info "正在修复端口配置..."

        # Stop docker services
        docker-compose down

        # Restart with correct port mapping
        docker-compose up -d

        print_success "端口配置已修复"
    fi
}

# Ensure system nginx is running
ensure_system_nginx_running() {
    print_info "检查系统nginx状态..."

    if ! systemctl is-active --quiet nginx; then
        print_warning "系统nginx未运行，正在启动..."

        # Test nginx configuration first
        if sudo nginx -t >/dev/null 2>&1; then
            sudo systemctl start nginx
            if systemctl is-active --quiet nginx; then
                print_success "系统nginx已启动"
            else
                print_error "系统nginx启动失败"
                return 1
            fi
        else
            print_error "nginx配置测试失败，请检查配置文件"
            return 1
        fi
    else
        print_success "系统nginx正在运行"
    fi
}

# Show main menu
show_menu() {
    echo -e "${PURPLE}请选择操作:${NC}"
    echo
    echo -e "${CYAN}🚀 服务管理${NC}"
    echo "  1) 查看服务状态"
    echo "  2) 启动服务"
    echo "  3) 停止服务"
    echo "  4) 重启服务"
    echo "  5) 查看服务日志"
    echo
    echo -e "${CYAN}📦 数据管理${NC}"
    echo "  6) 备份数据库"
    echo "  7) 恢复数据库"
    echo "  8) 查看备份列表"
    echo "  9) 自动备份管理"
    echo
    echo -e "${CYAN}🔄 项目更新${NC}"
    echo " 10) 安全更新项目配置"
    echo " 11) 快速更新选择"
    echo " 12) 更新New-API核心代码"
    echo " 13) 检查可用更新"
    echo
    echo -e "${CYAN}🌐 Nginx管理${NC}"
    echo " 14) 检查nginx状态"
    echo " 15) 重启系统nginx"
    echo " 16) 检查端口冲突"
    echo
    echo -e "${CYAN}⚙️  系统配置${NC}"
    echo " 17) 配置开机自启"
    echo " 18) 测试部署环境"
    echo " 19) 完整重新部署"
    echo
    echo -e "${CYAN}ℹ️  信息查看${NC}"
    echo " 20) 查看系统信息"
    echo " 21) 查看帮助文档"
    echo
    echo " 0) 退出"
    echo
}

# Service management functions
service_status() {
    print_info "检查服务状态..."
    ./scripts/manage-docker-services.sh status
}

service_start() {
    print_info "启动服务..."
    docker-compose up -d
    print_success "服务启动完成"
}

service_stop() {
    print_info "停止服务..."
    docker-compose down
    print_success "服务停止完成"
}

service_restart() {
    print_info "重启服务..."

    # Check for port conflicts before restarting
    check_port_conflicts

    docker-compose restart
    print_success "服务重启完成"

    # Ensure system nginx is running for proper proxy
    ensure_system_nginx_running
}

service_logs() {
    print_info "显示服务日志 (按Ctrl+C退出)..."
    docker-compose logs -f
}

# Database management functions
backup_database() {
    print_info "备份数据库..."

    # 确保在项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    cd "$SCRIPT_DIR"

    if [ -f "scripts/backup-database.sh" ]; then
        print_info "当前工作目录: $(pwd)"
        ./scripts/backup-database.sh
        echo
        print_info "备份完成！查看备份文件："
        ls -la backup/database_backup_*.sql | tail -3
    else
        print_error "备份脚本不存在: scripts/backup-database.sh"
    fi
}

restore_database() {
    print_info "恢复数据库..."

    # 检查新位置的脚本
    if [ -f "scripts/database/restore_database.sh" ]; then
        RESTORE_SCRIPT="scripts/database/restore_database.sh"
    elif [ -f "scripts/restore-database.sh" ]; then
        RESTORE_SCRIPT="scripts/restore-database.sh"
    else
        print_error "恢复脚本不存在"
        return 1
    fi

    echo "可用的备份文件:"
    ls -la backup/*.sql 2>/dev/null || echo "没有找到备份文件"
    echo
    read -p "请输入备份文件名 (或按Enter使用最新备份): " backup_file
    if [ -z "$backup_file" ]; then
        ./"$RESTORE_SCRIPT"
    else
        ./"$RESTORE_SCRIPT" "$backup_file"
    fi
}

list_backups() {
    print_info "数据库备份列表:"
    if [ -d "backup" ]; then
        ls -lah backup/*.sql 2>/dev/null || echo "没有找到备份文件"
    else
        echo "备份目录不存在"
    fi
}

auto_backup_manager() {
    print_info "启动自动备份管理器..."
    if [ -f "scripts/auto-backup-manager.sh" ]; then
        ./scripts/auto-backup-manager.sh
    else
        print_error "自动备份管理器不存在"
    fi
}

# Update functions
safe_update() {
    print_info "执行安全更新..."

    # 检查AI路径更新脚本
    if [ -f "scripts/maintenance/update-with-ai-path.sh" ]; then
        print_info "使用AI路径兼容更新脚本..."
        ./scripts/maintenance/update-with-ai-path.sh
    elif [ -f "scripts/update-project.sh" ]; then
        print_warning "使用传统更新脚本（可能不兼容AI路径）..."
        ./scripts/update-project.sh
    else
        print_error "更新脚本不存在"
    fi
}

quick_update() {
    print_info "执行快速更新..."
    if [ -f "scripts/quick-update.sh" ]; then
        ./scripts/quick-update.sh
    else
        print_error "快速更新脚本不存在"
    fi
}

update_core() {
    print_info "检测更新方式..."

    # 检查是否使用官方镜像
    if grep -q "image:.*calciumion/new-api:" docker-compose.yml; then
        print_info "检测到使用官方镜像，使用官方镜像更新脚本..."
        if [ -f "scripts/update-official-image.sh" ]; then
            ./scripts/update-official-image.sh
        else
            print_error "官方镜像更新脚本不存在"
        fi
    elif grep -q "build:" docker-compose.yml; then
        print_info "检测到使用本地构建，使用源码更新脚本..."
        if [ -f "scripts/update-new-api-core.sh" ]; then
            ./scripts/update-new-api-core.sh
        else
            print_error "源码更新脚本不存在"
        fi
    else
        print_error "无法检测更新方式，请检查docker-compose.yml配置"
    fi
}

check_updates() {
    print_info "检查可用更新..."
    if [ -f "scripts/quick-update.sh" ]; then
        ./scripts/quick-update.sh check
    else
        print_error "更新检查脚本不存在"
    fi
}

# Nginx management functions
nginx_status() {
    print_info "检查nginx状态..."
    echo
    echo "=== 系统nginx状态 ==="
    systemctl status nginx --no-pager -l
    echo
    echo "=== Docker nginx状态 ==="
    docker-compose ps nginx
    echo
    echo "=== 端口占用情况 ==="
    netstat -tlnp | grep ":80\|:443\|:8080"
}

nginx_restart() {
    print_info "重启系统nginx..."

    # Test configuration first
    if sudo nginx -t; then
        sudo systemctl restart nginx
        print_success "系统nginx重启完成"

        # Show status
        systemctl status nginx --no-pager -l
    else
        print_error "nginx配置测试失败，取消重启"
    fi
}

nginx_check_conflicts() {
    print_info "检查端口冲突..."

    echo "=== 端口80占用情况 ==="
    netstat -tlnp | grep ":80"
    echo
    echo "=== 端口443占用情况 ==="
    netstat -tlnp | grep ":443"
    echo
    echo "=== 端口8080占用情况 ==="
    netstat -tlnp | grep ":8080"
    echo

    # Check for conflicts
    if netstat -tlnp | grep ":80" | grep -q "docker-proxy"; then
        print_warning "发现Docker容器占用80端口！"
        print_info "建议运行端口冲突修复功能"
    else
        print_success "未发现端口冲突"
    fi
}



# System configuration
setup_autostart() {
    print_info "配置开机自启..."
    if [ -f "scripts/setup-autostart.sh" ]; then
        sudo ./scripts/setup-autostart.sh
    else
        print_error "自启动配置脚本不存在"
    fi
}

test_deployment() {
    print_info "测试部署环境..."

    # 检查新位置的测试脚本
    if [ -f "scripts/deployment/test-deployment.sh" ]; then
        ./scripts/deployment/test-deployment.sh
    elif [ -f "test-deployment.sh" ]; then
        ./test-deployment.sh
    else
        print_error "测试脚本不存在"
    fi
}

full_redeploy() {
    print_warning "这将重新部署整个项目"
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "执行完整重新部署..."
        sudo ./cmd.sh
    else
        print_info "操作已取消"
    fi
}

# System information
show_system_info() {
    print_info "系统信息:"
    echo "操作系统: $(lsb_release -d 2>/dev/null | cut -f2 || uname -a)"
    echo "Docker版本: $(docker --version 2>/dev/null || echo '未安装')"
    echo "Docker Compose版本: $(docker-compose --version 2>/dev/null || echo '未安装')"
    echo "Nginx状态: $(systemctl is-active nginx 2>/dev/null || echo '未知')"
    echo "项目版本: $(git describe --tags 2>/dev/null || git rev-parse --short HEAD 2>/dev/null || echo '未知')"
    echo "磁盘使用: $(df -h . | tail -1 | awk '{print $5}' || echo '未知')"
    echo
    print_info "服务状态:"
    docker-compose ps 2>/dev/null || echo "Docker服务未运行"
}

show_help() {
    echo -e "${CYAN}New-API 管理工具帮助${NC}"
    echo
    echo "这个工具整合了所有常用的管理功能:"
    echo
    echo "• 服务管理: 启动、停止、重启、查看状态和日志"
    echo "• 数据管理: 备份和恢复数据库"
    echo "• 项目更新: 安全更新和快速更新"
    echo "• 系统配置: 自启动配置和环境测试"
    echo
    echo "相关文档:"
    echo "• 项目完整指南: guide-newapi.md"
    echo "• 官方文档: README.md"
    echo
    echo "直接使用脚本:"
    echo "• 服务管理: ./scripts/manage-docker-services.sh [action]"
    echo "• 数据备份: ./scripts/backup-database.sh"
    echo "• AI路径更新: ./scripts/maintenance/update-with-ai-path.sh"
    echo "• 数据库恢复: ./scripts/database/restore_database.sh"
    echo
}

# Main function
main() {
    check_directory
    
    if [ $# -eq 0 ]; then
        # Interactive mode
        while true; do
            clear
            print_banner
            show_menu
            read -p "请输入选择 (0-21): " choice
            echo
            
            case $choice in
                1) service_status ;;
                2) service_start ;;
                3) service_stop ;;
                4) service_restart ;;
                5) service_logs ;;
                6) backup_database ;;
                7) restore_database ;;
                8) list_backups ;;
                9) auto_backup_manager ;;
                10) safe_update ;;
                11) quick_update ;;
                12) update_core ;;
                13) check_updates ;;
                14) nginx_status ;;
                15) nginx_restart ;;
                16) nginx_check_conflicts ;;
                17) setup_autostart ;;
                18) test_deployment ;;
                19) full_redeploy ;;
                20) show_system_info ;;
                21) show_help ;;
                0) print_info "再见!"; exit 0 ;;
                *) print_error "无效选择，请重试" ;;
            esac
            
            echo
            read -p "按Enter继续..."
        done
    else
        # Command line mode
        case "$1" in
            status) service_status ;;
            start) service_start ;;
            stop) service_stop ;;
            restart) service_restart ;;
            logs) service_logs ;;
            backup) backup_database ;;
            restore) restore_database ;;
            update) safe_update ;;
            quick-update) quick_update ;;
            update-core) update_core ;;
            check-updates) check_updates ;;
            nginx-status) nginx_status ;;
            nginx-restart) nginx_restart ;;
            nginx-check) nginx_check_conflicts ;;
            test) test_deployment ;;
            info) show_system_info ;;
            help|--help|-h) show_help ;;
            *)
                echo "用法: $0 [命令]"
                echo "可用命令: status, start, stop, restart, logs, backup, restore, update, quick-update, update-core, check-updates, nginx-status, nginx-restart, nginx-check, test, info, help"
                echo "或者不带参数运行进入交互模式"
                exit 1
                ;;
        esac
    fi
}

# Run main function
main "$@"
