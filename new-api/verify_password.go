package main

import (
	"fmt"
	"golang.org/x/crypto/bcrypt"
)

func main() {
	// YU用户的密码哈希 (从备份恢复)
	yuHash := "$2a$10$u0wMAtALZIejHyB7m0l0oebgJQpQ1s/kr.rqVNRPQfhmP1lurKJxi"

	// onefan用户的密码哈希 (从备份恢复)
	onefanHash := "$2a$10$bJDOw.WoL4VE4rcOChEJ7eoeEGwN.TdM.cMlIIg4lrKa4pQf9Wffi"

	// 测试不同的密码
	passwords := []string{"123", "123456", "YU", "onefan", "password", "admin", "root", "12345678", "qwerty", "111111", "000000", "888888", "666666", "yu123", "onefan123"}

	fmt.Println("测试YU用户的密码:")
	for _, password := range passwords {
		err := bcrypt.CompareHashAndPassword([]byte(yuHash), []byte(password))
		if err == nil {
			fmt.Printf("✓ YU用户密码 '%s' 匹配\n", password)
		}
	}

	fmt.Println("\n测试onefan用户的密码:")
	for _, password := range passwords {
		err := bcrypt.CompareHashAndPassword([]byte(onefanHash), []byte(password))
		if err == nil {
			fmt.Printf("✓ onefan用户密码 '%s' 匹配\n", password)
		}
	}
}
