# Nginx 新架构完整指南

## 📋 目录

1. [架构概述](#架构概述)
2. [设计理念](#设计理念)
3. [目录结构](#目录结构)
4. [配置管理](#配置管理)
5. [使用指南](#使用指南)
6. [最佳实践](#最佳实践)
7. [故障排除](#故障排除)
8. [迁移指南](#迁移指南)
9. [扩展开发](#扩展开发)
10. [维护手册](#维护手册)

---

## 🏗️ 架构概述

### 新架构特点

我们的新nginx架构采用**模块化设计**，将传统的单一配置文件拆分为多个可复用的组件，实现了：

- ✅ **模块化配置**：使用snippets实现配置片段复用
- ✅ **标准化管理**：统一的配置模板和管理脚本
- ✅ **自动化部署**：支持一键项目添加和配置生成
- ✅ **版本控制**：完整的配置备份和回滚机制
- ✅ **可扩展性**：新项目可快速集成到现有架构

### 架构对比

| 特性 | 传统架构 | 新架构 |
|------|---------|--------|
| 配置文件 | 单一大文件 | 模块化组件 |
| 配置复用 | 复制粘贴 | snippets复用 |
| 项目添加 | 手动编辑 | 模板生成 |
| 配置管理 | 手动操作 | 脚本自动化 |
| 错误恢复 | 手动回滚 | 自动备份恢复 |
| 扩展性 | 困难 | 简单 |

---

## 💡 设计理念

### 1. 关注点分离 (Separation of Concerns)

```
配置职责分离：
├── 主配置 (nginx.conf)     → 全局设置
├── 项目配置 (conf.d/)      → 项目特定设置
├── 配置片段 (snippets/)    → 可复用组件
├── 模板 (templates/)       → 配置生成模板
└── 脚本 (scripts/)         → 自动化管理
```

### 2. DRY原则 (Don't Repeat Yourself)

通过snippets避免配置重复：
- SSL配置在所有项目间复用
- 代理参数标准化
- 安全头统一管理
- 缓存策略一致应用

### 3. 配置即代码 (Configuration as Code)

- 所有配置文件版本控制
- 模板化配置生成
- 自动化测试和部署
- 完整的变更记录

### 4. 渐进式增强 (Progressive Enhancement)

- 保持向后兼容
- 平滑迁移路径
- 零停机更新
- 完整回滚支持

---

## 📁 目录结构

### 完整目录树

```
/root/workspace/shared/nginx/
├── 📂 conf.d/                    # 项目配置文件
│   ├── liangliangdamowang.edu.deal.conf  # 主站完整配置
│   ├── new-api.conf              # New-API项目配置
│   ├── love.conf                 # Love项目配置
│   ├── gpt-load.conf             # GPT-Load项目配置
│   └── README.md                 # 配置文件说明
├── 📂 snippets/                  # 可复用配置片段
│   ├── ssl-params.conf           # SSL/TLS安全配置
│   ├── proxy-params.conf         # 标准代理配置
│   ├── security-headers.conf     # 安全头配置
│   ├── cors-headers.conf         # CORS跨域配置
│   └── cache-static.conf         # 静态文件缓存
├── 📂 templates/                 # 配置模板
│   ├── project.tpl               # 通用项目模板
│   └── spa.tpl                   # 单页应用模板
├── 📂 scripts/                   # 管理脚本
│   ├── nginx-add-project.sh      # 添加项目脚本
│   ├── nginx-enable-site.sh      # 启用站点脚本
│   ├── nginx-disable-site.sh     # 禁用站点脚本
│   └── nginx-backup.sh           # 配置备份脚本
├── 📂 backup/                    # 配置备份
│   ├── production-20250725-211600/  # 生产环境备份
│   │   ├── nginx.conf            # 主配置备份
│   │   ├── sites-available/      # 站点配置备份
│   │   ├── sites-enabled/        # 启用站点备份
│   │   ├── ssl-info/             # SSL证书信息
│   │   ├── restore.sh            # 自动恢复脚本
│   │   ├── backup-manifest.txt   # 备份清单
│   │   └── deployment-report.md  # 部署报告
│   └── [timestamp]/              # 历史备份
├── 📂 ssl/                       # SSL证书备份（现有）
├── 📄 system-nginx.conf          # 系统配置文件（现有）
├── 📄 default.conf               # 默认配置（现有）
└── 📄 README.md                  # 架构说明文档
```

### 目录功能说明

#### conf.d/ - 项目配置目录
存放各个项目的nginx配置文件，每个项目一个独立的.conf文件。

#### snippets/ - 配置片段目录
存放可复用的配置片段，通过include指令在项目配置中引用。

#### templates/ - 模板目录
存放配置模板文件，支持占位符替换，用于快速生成新项目配置。

#### scripts/ - 脚本目录
存放nginx管理相关的自动化脚本。

#### backup/ - 备份目录
存放nginx配置的历史备份，支持完整的配置恢复。

---

## ⚙️ 配置管理

### 🎯 新架构配置方式

**重要更新**：从2025年7月26日起，nginx配置已迁移到统一管理模式：

- **配置目录**：`/root/workspace/shared/nginx/` 现在是nginx的实际配置目录
- **主配置文件**：nginx.conf 直接引用 `shared/nginx/conf.d/*.conf`
- **无需同步**：直接在 `shared/nginx` 目录中修改配置即可生效
- **统一管理**：所有配置文件、snippets、模板都在一个位置

#### 配置文件路径变更

```bash
# 旧方式（已废弃）
/etc/nginx/conf.d/*.conf

# 新方式（当前使用）
/root/workspace/shared/nginx/conf.d/*.conf
```

### 核心管理工具

我们提供了强大的nginx配置管理工具：`nginx-manager.sh`

```bash
# 查看帮助
./deploy-all/scripts/nginx-manager.sh --help

# 查看系统状态
./deploy-all/scripts/nginx-manager.sh --status

# 备份当前配置
./deploy-all/scripts/nginx-manager.sh --backup

# 测试配置语法
./deploy-all/scripts/nginx-manager.sh --test
```

### 项目管理功能

#### 查看可用模板
```bash
./deploy-all/scripts/nginx-manager.sh --list-templates
```

#### 添加新项目
```bash
# 基本语法
./deploy-all/scripts/nginx-manager.sh --add-project <domain> <project_name> <template>

# 示例：添加一个新的API项目
./deploy-all/scripts/nginx-manager.sh --add-project api.example.com myapi project

# 示例：添加一个SPA应用
./deploy-all/scripts/nginx-manager.sh --add-project app.example.com webapp spa
```

#### 启用/禁用站点
```bash
# 启用站点
./deploy-all/scripts/nginx-manager.sh --enable-site example.com

# 禁用站点
./deploy-all/scripts/nginx-manager.sh --disable-site example.com
```

#### SSL证书管理
```bash
# 申请SSL证书
./deploy-all/scripts/nginx-manager.sh --ssl-cert example.com <EMAIL>
```

### 配置文件管理

#### Snippets使用方法

在项目配置文件中使用include指令引用snippets：

```nginx
server {
    listen 443 ssl http2;
    server_name example.com;
    
    # 引用SSL配置片段
    include /root/workspace/shared/nginx/snippets/ssl-params.conf;
    
    # 引用安全头配置
    include /root/workspace/shared/nginx/snippets/security-headers.conf;
    
    location / {
        proxy_pass http://backend;
        
        # 引用代理配置片段
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
    }
    
    location /api/ {
        proxy_pass http://backend;
        
        # 引用代理和CORS配置
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        include /root/workspace/shared/nginx/snippets/cors-headers.conf;
    }
}
```

---

## 📖 使用指南

### 快速开始

#### 1. 环境准备

确保你有以下环境：
- Ubuntu/Debian系统
- Nginx已安装
- 具有sudo权限的用户账户

#### 2. 检查当前状态

```bash
# 检查nginx服务状态
sudo systemctl status nginx

# 检查配置语法
sudo nginx -t

# 查看当前配置
./deploy-all/scripts/nginx-manager.sh --status
```

#### 3. 创建第一个项目

```bash
# 1. 查看可用模板
./deploy-all/scripts/nginx-manager.sh --list-templates

# 2. 添加新项目
./deploy-all/scripts/nginx-manager.sh --add-project mysite.com myproject project

# 3. 启用站点
./deploy-all/scripts/nginx-manager.sh --enable-site mysite.com

# 4. 申请SSL证书
./deploy-all/scripts/nginx-manager.sh --ssl-cert mysite.com <EMAIL>

# 5. 测试访问
curl -I https://mysite.com/
```

### 日常操作

#### 配置更新流程

```bash
# 1. 备份当前配置
./deploy-all/scripts/nginx-manager.sh --backup

# 2. 修改配置文件
vim /root/workspace/shared/nginx/conf.d/mysite.com.conf

# 3. 测试配置语法
nginx -t

# 4. 应用配置
systemctl reload nginx

# 5. 验证功能
curl -I https://mysite.com/
```

#### 批量操作

```bash
# 批量备份所有配置
for site in $(ls /etc/nginx/conf.d/*.conf); do
    ./deploy-all/scripts/nginx-manager.sh --backup
done

# 批量测试所有站点
for site in site1.com site2.com site3.com; do
    curl -I https://$site/ || echo "$site failed"
done
```

### 项目类型配置

#### 1. 标准Web应用

```bash
# 使用project模板
./deploy-all/scripts/nginx-manager.sh --add-project webapp.com myapp project 127.0.0.1 3000 /
```

生成的配置包含：
- HTTP到HTTPS重定向
- SSL/TLS配置
- 反向代理到后端服务
- 静态文件缓存
- 安全头设置

#### 2. 单页应用 (SPA)

```bash
# 使用spa模板
./deploy-all/scripts/nginx-manager.sh --add-project spa.com myapp spa
```

SPA配置特点：
- 支持前端路由
- 静态资源优化缓存
- API代理配置
- 构建资源处理

#### 3. API服务

```bash
# API服务配置
./deploy-all/scripts/nginx-manager.sh --add-project api.com myapi project 127.0.0.1 8080 /api/
```

API配置包含：
- CORS跨域支持
- 请求体大小限制
- 超时设置优化
- 错误处理

#### 4. 微服务架构

对于微服务，可以创建多个配置文件：

```bash
# 用户服务
./deploy-all/scripts/nginx-manager.sh --add-project api.com user-service project 127.0.0.1 3001 /api/users/

# 订单服务
./deploy-all/scripts/nginx-manager.sh --add-project api.com order-service project 127.0.0.1 3002 /api/orders/

# 支付服务
./deploy-all/scripts/nginx-manager.sh --add-project api.com payment-service project 127.0.0.1 3003 /api/payments/
```

---

## 🎯 最佳实践

### 1. 配置组织原则

#### 文件命名规范
```
# 域名作为文件名
example.com.conf
api.example.com.conf
admin.example.com.conf

# 项目名称作为upstream名称
upstream example_com_backend { ... }
upstream api_example_com_backend { ... }
```

#### 配置结构标准
```nginx
# 1. 注释说明
# Project: Example Website
# Domain: example.com
# Backend: 127.0.0.1:3000

# 2. HTTP重定向
server {
    listen 80;
    server_name example.com;
    return 301 https://$server_name$request_uri;
}

# 3. Upstream定义
upstream example_com_backend {
    server 127.0.0.1:3000;
    keepalive 32;
}

# 4. HTTPS服务器
server {
    listen 443 ssl http2;
    server_name example.com;

    # SSL配置
    ssl_certificate /etc/letsencrypt/live/example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/example.com/privkey.pem;

    # 引用snippets
    include /root/workspace/shared/nginx/snippets/ssl-params.conf;
    include /root/workspace/shared/nginx/snippets/security-headers.conf;

    # Location配置
    location / {
        proxy_pass http://example_com_backend;
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
    }
}
```

### 2. 安全配置

#### SSL/TLS最佳实践
```nginx
# 使用现代SSL配置
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;

# 启用HSTS
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# 防止点击劫持
add_header X-Frame-Options DENY always;
```

#### 访问控制
```nginx
# IP白名单
location /admin/ {
    allow ***********/24;
    allow 10.0.0.0/8;
    deny all;

    proxy_pass http://backend;
}

# 基本认证
location /private/ {
    auth_basic "Restricted Area";
    auth_basic_user_file /etc/nginx/.htpasswd;

    proxy_pass http://backend;
}
```

### 3. 性能优化

#### 缓存策略
```nginx
# 静态资源长期缓存
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary Accept-Encoding;
}

# API响应短期缓存
location /api/public/ {
    proxy_pass http://backend;
    proxy_cache api_cache;
    proxy_cache_valid 200 5m;
    proxy_cache_key "$scheme$request_method$host$request_uri";
}
```

#### 压缩配置
```nginx
# Gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/javascript
    application/xml+rss
    application/json;
```

#### 连接优化
```nginx
# Upstream连接池
upstream backend {
    server 127.0.0.1:3000;
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# 代理连接优化
proxy_http_version 1.1;
proxy_set_header Connection "";
proxy_connect_timeout 5s;
proxy_send_timeout 60s;
proxy_read_timeout 60s;
```

### 4. 监控和日志

#### 访问日志配置
```nginx
# 自定义日志格式
log_format detailed '$remote_addr - $remote_user [$time_local] '
                   '"$request" $status $body_bytes_sent '
                   '"$http_referer" "$http_user_agent" '
                   '$request_time $upstream_response_time';

# 项目特定日志
access_log /var/log/nginx/example.com.access.log detailed;
error_log /var/log/nginx/example.com.error.log warn;
```

#### 健康检查
```nginx
# 健康检查端点
location /health {
    access_log off;
    return 200 "healthy\n";
    add_header Content-Type text/plain;
}

# 状态监控
location /nginx_status {
    stub_status on;
    access_log off;
    allow 127.0.0.1;
    deny all;
}
```

---

## 🔧 故障排除

### 常见问题诊断

#### 1. 配置语法错误

**症状**：nginx -t 报错
```bash
nginx: [emerg] duplicate upstream "backend" in /etc/nginx/conf.d/site.conf:10
```

**解决方案**：
```bash
# 1. 检查重复定义
grep -r "upstream backend" /etc/nginx/

# 2. 使用唯一的upstream名称
upstream site_backend { ... }
upstream api_backend { ... }

# 3. 重新测试
nginx -t
```

#### 2. SSL证书问题

**症状**：SSL握手失败
```bash
curl: (60) SSL certificate problem: certificate has expired
```

**解决方案**：
```bash
# 1. 检查证书状态
certbot certificates

# 2. 续期证书
certbot renew

# 3. 测试SSL配置
openssl s_client -connect example.com:443 -servername example.com
```

#### 3. 代理连接失败

**症状**：502 Bad Gateway
```bash
2025/07/25 21:30:00 [error] connect() failed (111: Connection refused) while connecting to upstream
```

**解决方案**：
```bash
# 1. 检查后端服务状态
systemctl status myapp
netstat -tlnp | grep :3000

# 2. 检查防火墙
ufw status
iptables -L

# 3. 测试后端连接
curl -I http://127.0.0.1:3000/

# 4. 检查nginx配置
nginx -T | grep -A 10 "upstream.*backend"
```

#### 4. 权限问题

**症状**：403 Forbidden
```bash
2025/07/25 21:30:00 [error] open() "/var/www/html/index.html" failed (13: Permission denied)
```

**解决方案**：
```bash
# 1. 检查文件权限
ls -la /var/www/html/

# 2. 修复权限
chown -R www-data:www-data /var/www/html/
chmod -R 755 /var/www/html/

# 3. 检查SELinux（如果适用）
getenforce
setsebool -P httpd_can_network_connect 1
```

### 诊断工具和命令

#### 配置检查
```bash
# 语法检查
nginx -t

# 配置转储
nginx -T

# 检查include文件
nginx -T | grep include

# 查找配置冲突
./deploy-all/scripts/nginx-manager.sh --conflicts
```

#### 服务状态
```bash
# 服务状态
systemctl status nginx

# 进程信息
ps aux | grep nginx

# 端口监听
netstat -tlnp | grep nginx
ss -tlnp | grep nginx
```

#### 日志分析
```bash
# 实时错误日志
tail -f /var/log/nginx/error.log

# 访问日志分析
tail -f /var/log/nginx/access.log

# 特定站点日志
tail -f /var/log/nginx/example.com.error.log

# 日志统计
awk '{print $9}' /var/log/nginx/access.log | sort | uniq -c | sort -nr
```

#### 网络测试
```bash
# 本地测试
curl -I http://localhost/
curl -I https://localhost/

# 外部测试
curl -I http://example.com/
curl -I https://example.com/

# SSL测试
openssl s_client -connect example.com:443 -servername example.com

# DNS解析
nslookup example.com
dig example.com
```

### 紧急恢复程序

#### 快速回滚
```bash
# 1. 停止nginx
systemctl stop nginx

# 2. 恢复备份配置
cd /root/workspace/shared/nginx/backup/production-20250725-211600/
./restore.sh

# 3. 验证恢复
nginx -t
systemctl start nginx
```

#### 手动恢复
```bash
# 1. 删除问题配置
rm /etc/nginx/conf.d/problematic-site.conf

# 2. 恢复工作配置
cp /root/workspace/shared/nginx/backup/production-*/sites-available/* /etc/nginx/sites-available/
ln -sf /etc/nginx/sites-available/liangliangdamowang.edu.deal.conf /etc/nginx/sites-enabled/

# 3. 重启服务
nginx -t && systemctl reload nginx
```

---

## 🚀 迁移指南

### 从传统架构迁移

#### 迁移前准备

1. **完整备份**
```bash
# 创建备份目录
mkdir -p /root/nginx-migration-backup/$(date +%Y%m%d)

# 备份所有配置
cp -r /etc/nginx/ /root/nginx-migration-backup/$(date +%Y%m%d)/

# 备份SSL证书信息
certbot certificates > /root/nginx-migration-backup/$(date +%Y%m%d)/ssl-status.txt
```

2. **环境检查**
```bash
# 检查当前配置
nginx -t

# 记录当前状态
systemctl status nginx > /root/nginx-migration-backup/$(date +%Y%m%d)/service-status.txt

# 测试所有站点
for site in $(nginx -T | grep server_name | awk '{print $2}' | sed 's/;//g' | sort -u); do
    echo "Testing $site"
    curl -I http://$site/ || echo "Failed: $site"
done > /root/nginx-migration-backup/$(date +%Y%m%d)/site-tests.txt
```

#### 迁移步骤

1. **安装新架构**
```bash
# 创建新架构目录
mkdir -p /root/workspace/shared/nginx/{conf.d,snippets,templates,scripts,backup}

# 复制snippets
cp /root/workspace/shared/nginx/snippets/* /root/workspace/shared/nginx/snippets/

# 复制模板
cp /root/workspace/shared/nginx/templates/* /root/workspace/shared/nginx/templates/
```

2. **转换现有配置**
```bash
# 分析现有配置
./deploy-all/scripts/nginx-manager.sh --analyze-config /etc/nginx/sites-available/

# 生成新配置
./deploy-all/scripts/nginx-manager.sh --convert-config /etc/nginx/sites-available/mysite.conf
```

3. **测试新配置**
```bash
# 在测试环境验证
nginx -t -c /root/workspace/shared/nginx/test-config.conf

# 逐步迁移
./deploy-all/scripts/nginx-manager.sh --migrate-site mysite.com
```

4. **切换到新架构**
```bash
# 禁用旧配置
rm /etc/nginx/sites-enabled/*

# 启用新配置
cp /root/workspace/shared/nginx/conf.d/*.conf /etc/nginx/conf.d/

# 重载配置
nginx -t && systemctl reload nginx
```

#### 迁移验证

```bash
# 功能测试
./deploy-all/scripts/nginx-manager.sh --test-all-sites

# 性能对比
ab -n 1000 -c 10 http://example.com/ > new-arch-performance.txt

# SSL测试
./deploy-all/scripts/nginx-manager.sh --test-ssl-all
```

### 回滚计划

如果迁移出现问题，可以快速回滚：

```bash
# 自动回滚
/root/workspace/shared/nginx/backup/production-*/restore.sh

# 手动回滚
systemctl stop nginx
rm /etc/nginx/conf.d/*
cp -r /root/nginx-migration-backup/$(date +%Y%m%d)/nginx/* /etc/nginx/
nginx -t && systemctl start nginx
```

---

## 🔨 扩展开发

### 创建自定义Snippets

#### 1. 创建新的配置片段

```bash
# 创建自定义缓存配置
cat > /root/workspace/shared/nginx/snippets/custom-cache.conf << 'EOF'
# Custom caching configuration
# Optimized for high-traffic applications

# Proxy cache configuration
proxy_cache_path /var/cache/nginx/custom levels=1:2 keys_zone=custom_cache:10m max_size=1g inactive=60m use_temp_path=off;

# Cache settings
proxy_cache custom_cache;
proxy_cache_valid 200 302 10m;
proxy_cache_valid 404 1m;
proxy_cache_use_stale error timeout invalid_header updating http_500 http_502 http_503 http_504;
proxy_cache_lock on;
proxy_cache_lock_timeout 5s;

# Cache headers
add_header X-Cache-Status $upstream_cache_status;
EOF
```

#### 2. 创建项目模板

```bash
# 创建微服务模板
cat > /root/workspace/shared/nginx/templates/microservice.tpl << 'EOF'
# Microservice configuration template
# Optimized for containerized applications

# HTTP redirect
server {
    listen 80;
    server_name __DOMAIN__;
    return 301 https://$server_name$request_uri;
}

# Load balancer upstream
upstream __PROJECT_NAME___backend {
    least_conn;
    server __BACKEND_HOST__:__BACKEND_PORT__ max_fails=3 fail_timeout=30s;
    server __BACKUP_HOST__:__BACKUP_PORT__ backup;
    keepalive 32;
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name __DOMAIN__;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/__DOMAIN__/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/__DOMAIN__/privkey.pem;

    include /root/workspace/shared/nginx/snippets/ssl-params.conf;
    include /root/workspace/shared/nginx/snippets/security-headers.conf;

    # API endpoints
    location __LOCATION_PATH__ {
        proxy_pass http://__PROJECT_NAME___backend;
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        include /root/workspace/shared/nginx/snippets/cors-headers.conf;
        include /root/workspace/shared/nginx/snippets/custom-cache.conf;

        # Microservice specific settings
        proxy_set_header X-Service-Name __PROJECT_NAME__;
        proxy_set_header X-Request-ID $request_id;
    }

    # Health check
    location __LOCATION_PATH__health {
        proxy_pass http://__PROJECT_NAME___backend;
        access_log off;
    }

    # Metrics endpoint
    location __LOCATION_PATH__metrics {
        proxy_pass http://__PROJECT_NAME___backend;
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        deny all;
    }
}
EOF
```

### 扩展管理脚本

#### 添加自定义功能到nginx-manager.sh

```bash
# 在nginx-manager.sh中添加新功能
add_custom_function() {
    local function_name="$1"
    local function_code="$2"

    # 添加到脚本末尾
    cat >> /root/workspace/deploy-all/scripts/nginx-manager.sh << EOF

# Custom function: $function_name
$function_code
EOF
}

# 示例：添加批量SSL证书续期功能
add_custom_function "renew_all_ssl" '
renew_all_ssl() {
    log_section "批量续期SSL证书"

    # 获取所有域名
    local domains=$(nginx -T | grep server_name | awk "{print \$2}" | sed "s/;//g" | sort -u)

    for domain in $domains; do
        if [[ "$domain" != "localhost" && "$domain" != "_" ]]; then
            log_info "续期证书: $domain"
            certbot renew --cert-name "$domain" --quiet || log_warning "续期失败: $domain"
        fi
    done

    # 重载nginx
    systemctl reload nginx
    log_success "SSL证书批量续期完成"
}
'
```

### 监控集成

#### 添加Prometheus监控

```bash
# 创建监控配置片段
cat > /root/workspace/shared/nginx/snippets/prometheus-monitoring.conf << 'EOF'
# Prometheus monitoring configuration

# Metrics endpoint
location /metrics {
    stub_status on;
    access_log off;
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    deny all;
}

# Request metrics
log_format prometheus '$remote_addr - $remote_user [$time_local] '
                     '"$request" $status $body_bytes_sent '
                     '"$http_referer" "$http_user_agent" '
                     'rt=$request_time uct="$upstream_connect_time" '
                     'uht="$upstream_header_time" urt="$upstream_response_time"';
EOF
```

#### 集成日志分析

```bash
# 创建日志分析脚本
cat > /root/workspace/shared/nginx/scripts/analyze-logs.sh << 'EOF'
#!/bin/bash

# Nginx日志分析脚本
# 提供访问统计、错误分析、性能监控

analyze_access_logs() {
    local log_file="${1:-/var/log/nginx/access.log}"

    echo "=== 访问统计 ==="
    echo "总请求数: $(wc -l < "$log_file")"
    echo "独立IP数: $(awk '{print $1}' "$log_file" | sort -u | wc -l)"

    echo -e "\n=== 状态码分布 ==="
    awk '{print $9}' "$log_file" | sort | uniq -c | sort -nr

    echo -e "\n=== 热门页面 ==="
    awk '{print $7}' "$log_file" | sort | uniq -c | sort -nr | head -10

    echo -e "\n=== 响应时间分析 ==="
    awk '{print $NF}' "$log_file" | sort -n | awk '
        BEGIN { sum = 0; count = 0; }
        { sum += $1; count++; values[count] = $1; }
        END {
            print "平均响应时间: " sum/count "s"
            print "中位数响应时间: " values[int(count/2)] "s"
            print "最大响应时间: " values[count] "s"
        }
    '
}

# 执行分析
analyze_access_logs "$@"
EOF

chmod +x /root/workspace/shared/nginx/scripts/analyze-logs.sh
```

---

## 📚 维护手册

### 日常维护任务

#### 每日检查清单

```bash
#!/bin/bash
# 每日nginx健康检查脚本

echo "=== Nginx每日健康检查 $(date) ==="

# 1. 服务状态检查
echo "1. 检查服务状态..."
if systemctl is-active --quiet nginx; then
    echo "   ✅ Nginx服务运行正常"
else
    echo "   ❌ Nginx服务异常"
    systemctl status nginx
fi

# 2. 配置语法检查
echo "2. 检查配置语法..."
if nginx -t &>/dev/null; then
    echo "   ✅ 配置语法正确"
else
    echo "   ❌ 配置语法错误"
    nginx -t
fi

# 3. SSL证书检查
echo "3. 检查SSL证书..."
for domain in $(nginx -T | grep server_name | awk '{print $2}' | sed 's/;//g' | sort -u); do
    if [[ "$domain" != "localhost" && "$domain" != "_" ]]; then
        expiry=$(echo | openssl s_client -servername "$domain" -connect "$domain":443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
        if [[ -n "$expiry" ]]; then
            days_left=$(( ($(date -d "$expiry" +%s) - $(date +%s)) / 86400 ))
            if [[ $days_left -lt 30 ]]; then
                echo "   ⚠️  $domain 证书将在 $days_left 天后过期"
            else
                echo "   ✅ $domain 证书有效 ($days_left 天)"
            fi
        fi
    fi
done

# 4. 磁盘空间检查
echo "4. 检查磁盘空间..."
disk_usage=$(df /var/log/nginx | tail -1 | awk '{print $5}' | sed 's/%//')
if [[ $disk_usage -gt 80 ]]; then
    echo "   ⚠️  日志目录磁盘使用率: ${disk_usage}%"
else
    echo "   ✅ 日志目录磁盘使用率: ${disk_usage}%"
fi

# 5. 错误日志检查
echo "5. 检查最近错误..."
error_count=$(tail -100 /var/log/nginx/error.log | grep "$(date '+%Y/%m/%d')" | wc -l)
if [[ $error_count -gt 10 ]]; then
    echo "   ⚠️  今日错误日志条数: $error_count"
    echo "   最近错误:"
    tail -5 /var/log/nginx/error.log
else
    echo "   ✅ 今日错误日志条数: $error_count"
fi

echo "=== 检查完成 ==="
```

#### 每周维护任务

```bash
#!/bin/bash
# 每周nginx维护脚本

echo "=== Nginx每周维护 $(date) ==="

# 1. 日志轮转
echo "1. 执行日志轮转..."
logrotate -f /etc/logrotate.d/nginx

# 2. 清理旧备份
echo "2. 清理旧备份..."
find /root/workspace/shared/nginx/backup/ -type d -mtime +30 -exec rm -rf {} \; 2>/dev/null

# 3. 配置备份
echo "3. 创建配置备份..."
./deploy-all/scripts/nginx-manager.sh --backup

# 4. 性能分析
echo "4. 生成性能报告..."
/root/workspace/shared/nginx/scripts/analyze-logs.sh > /tmp/nginx-weekly-report.txt

# 5. SSL证书续期
echo "5. 尝试SSL证书续期..."
certbot renew --quiet

echo "=== 维护完成 ==="
```

#### 每月安全审计

```bash
#!/bin/bash
# 每月nginx安全审计脚本

echo "=== Nginx每月安全审计 $(date) ==="

# 1. 检查SSL配置
echo "1. SSL安全配置检查..."
for domain in $(nginx -T | grep server_name | awk '{print $2}' | sed 's/;//g' | sort -u); do
    if [[ "$domain" != "localhost" && "$domain" != "_" ]]; then
        echo "检查 $domain SSL配置..."
        # 使用testssl.sh或类似工具检查SSL配置
        # testssl.sh --quiet --color 0 "$domain" || echo "SSL检查失败: $domain"
    fi
done

# 2. 检查安全头
echo "2. 安全头检查..."
curl -I https://liangliangdamowang.edu.deal/ | grep -E "(X-Frame-Options|X-Content-Type-Options|Strict-Transport-Security)"

# 3. 检查访问日志异常
echo "3. 异常访问检查..."
awk '$9 >= 400 {print $0}' /var/log/nginx/access.log | tail -20

# 4. 检查配置安全性
echo "4. 配置安全性检查..."
nginx -T | grep -E "(server_tokens|autoindex)" || echo "未发现明显安全问题"

echo "=== 安全审计完成 ==="
```

### 性能调优

#### 系统级优化

```bash
# 1. 内核参数优化
cat >> /etc/sysctl.conf << 'EOF'
# Nginx性能优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 10
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000
EOF

sysctl -p
```

#### Nginx配置优化

```nginx
# 工作进程优化
worker_processes auto;
worker_cpu_affinity auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 65535;
    use epoll;
    multi_accept on;
}

http {
    # 缓冲区优化
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # 超时优化
    client_header_timeout 3m;
    client_body_timeout 3m;
    send_timeout 3m;

    # 连接优化
    keepalive_timeout 65;
    keepalive_requests 100;

    # 压缩优化
    gzip on;
    gzip_min_length 1k;
    gzip_buffers 4 16k;
    gzip_comp_level 2;
    gzip_types text/plain application/javascript text/css application/xml text/javascript;
    gzip_vary on;
}
```

### 监控和告警

#### 集成监控系统

```bash
# 创建监控配置
cat > /etc/nginx/conf.d/monitoring.conf << 'EOF'
server {
    listen 8080;
    server_name localhost;

    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        deny all;
    }

    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF
```

#### 告警脚本

```bash
#!/bin/bash
# Nginx告警脚本

ALERT_EMAIL="<EMAIL>"
THRESHOLD_ERROR_COUNT=50
THRESHOLD_RESPONSE_TIME=5.0

# 检查错误率
error_count=$(tail -1000 /var/log/nginx/access.log | awk '$9 >= 500 {count++} END {print count+0}')
if [[ $error_count -gt $THRESHOLD_ERROR_COUNT ]]; then
    echo "Nginx错误率过高: $error_count 个5xx错误" | mail -s "Nginx告警" "$ALERT_EMAIL"
fi

# 检查响应时间
avg_response_time=$(tail -1000 /var/log/nginx/access.log | awk '{sum+=$NF; count++} END {print sum/count}')
if (( $(echo "$avg_response_time > $THRESHOLD_RESPONSE_TIME" | bc -l) )); then
    echo "Nginx响应时间过慢: ${avg_response_time}s" | mail -s "Nginx性能告警" "$ALERT_EMAIL"
fi
```

### 文档维护

#### 配置变更记录

每次配置变更都应该记录：

```bash
# 创建变更记录模板
cat > /root/workspace/shared/nginx/CHANGELOG.md << 'EOF'
# Nginx配置变更记录

## [1.2.0] - 2025-07-25

### 新增
- 添加GPT-Load项目配置
- 新增微服务配置模板
- 集成Prometheus监控

### 修改
- 优化SSL配置参数
- 更新安全头设置
- 改进缓存策略

### 修复
- 修复upstream重复定义问题
- 解决CORS配置冲突
- 修正日志格式错误

### 删除
- 移除过时的配置片段
- 清理无用的备份文件
EOF
```

#### 知识库维护

定期更新文档：

```bash
# 自动生成配置文档
./deploy-all/scripts/nginx-manager.sh --generate-docs

# 更新README文件
./deploy-all/scripts/nginx-manager.sh --update-readme

# 生成配置图表
./deploy-all/scripts/nginx-manager.sh --generate-diagrams
```

---

## 📖 总结

### 架构优势总结

我们的新nginx架构提供了以下核心优势：

1. **🔧 模块化设计**
   - 配置片段可复用，减少重复代码
   - 项目配置独立，便于维护和扩展
   - 模板化生成，快速部署新项目

2. **🚀 自动化管理**
   - 一键项目添加和配置生成
   - 自动化SSL证书申请和续期
   - 智能配置冲突检测和解决

3. **🛡️ 安全性增强**
   - 标准化安全配置
   - 现代SSL/TLS设置
   - 完整的安全头配置

4. **📊 可观测性**
   - 详细的访问和错误日志
   - 性能监控和分析工具
   - 健康检查和状态监控

5. **🔄 可靠性保障**
   - 完整的配置备份机制
   - 自动回滚和恢复功能
   - 零停机配置更新

### 最佳实践要点

- ✅ 始终在修改配置前创建备份
- ✅ 使用nginx -t测试配置语法
- ✅ 采用渐进式部署策略
- ✅ 定期更新SSL证书
- ✅ 监控系统性能和错误日志
- ✅ 保持配置文档的及时更新

### 持续改进

这个架构是一个持续演进的系统，我们建议：

1. **定期评估**：每季度评估架构的有效性
2. **社区反馈**：收集用户反馈，持续优化
3. **技术更新**：跟进nginx和相关技术的最新发展
4. **安全审计**：定期进行安全配置审计
5. **性能优化**：基于监控数据持续优化性能

### 获取帮助

如果在使用过程中遇到问题，可以：

- 查看本文档的故障排除章节
- 使用 `nginx-manager.sh --help` 获取命令帮助
- 检查 `/var/log/nginx/` 下的日志文件
- 参考nginx官方文档和社区资源

---

**文档版本**: v1.0
**最后更新**: 2025-07-25
**维护者**: Workspace项目团队

---

*这份文档将随着架构的发展持续更新，请定期查看最新版本。*
