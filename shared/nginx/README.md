# Nginx 配置管理系统

## 🎯 重要说明

**此目录现在是nginx的实际生产配置目录！**

从2025年7月26日起，nginx主配置文件直接引用此目录：
- nginx.conf 中的 `include /root/workspace/shared/nginx/conf.d/*.conf;`
- 直接在此目录修改配置即可生效，无需同步到其他位置
- 所有配置管理都在此统一进行

## 📁 目录结构

这是一个标准化的nginx配置管理系统，基于最佳实践设计，支持模块化、可扩展的配置管理。

```
shared/nginx/
├── 📂 conf.d/                    # 项目配置文件目录
│   ├── new-api.conf              # New-API项目配置
│   ├── love.conf                 # Love项目配置
│   └── gpt-load.conf             # GPT-Load项目配置
├── 📂 snippets/                  # 可复用配置片段
│   ├── ssl-params.conf           # SSL/TLS安全配置
│   ├── proxy-params.conf         # 代理参数配置
│   └── security-headers.conf     # 安全头配置
├── 📂 templates/                 # 配置模板
│   └── project.tpl               # 项目配置模板
├── 📂 scripts/                   # 管理脚本
│   ├── nginx-add-project.sh      # 添加项目脚本
│   ├── nginx-enable-site.sh      # 启用站点脚本
│   ├── nginx-disable-site.sh     # 禁用站点脚本
│   └── nginx-backup.sh           # 配置备份脚本
├── 📂 backup/                    # 配置备份
│   ├── current-production/       # 当前生产配置备份
│   └── [timestamp]/              # 历史备份
├── 📂 ssl/                       # SSL证书备份（现有）
├── 📄 system-nginx.conf          # 系统配置文件（现有）
└── 📄 default.conf               # 默认配置（现有）
```

## 🎯 设计原则

### 1. 模块化设计
- **配置分离**: 每个项目独立配置文件
- **片段复用**: 通用配置提取为snippets
- **模板化**: 使用模板快速生成配置

### 2. 标准化管理
- **统一目录**: 所有配置集中管理
- **版本控制**: 支持配置版本管理
- **自动化**: 脚本化配置管理

### 3. 可扩展性
- **新项目**: 快速添加新项目配置
- **配置复用**: snippets可在多项目间复用
- **灵活部署**: 支持不同部署场景

## 🔧 使用方法

### 添加新项目
```bash
cd /root/workspace/shared/nginx/scripts
./nginx-add-project.sh <域名> <项目名>
```

### 启用/禁用站点
```bash
./nginx-enable-site.sh <域名>
./nginx-disable-site.sh <域名>
```

### 备份配置
```bash
./nginx-backup.sh
```

## 📋 配置文件说明

### conf.d/ - 项目配置
- 每个项目一个独立的.conf文件
- 使用include指令引用snippets
- 支持项目特定的配置

### snippets/ - 配置片段
- ssl-params.conf: 现代SSL/TLS配置
- proxy-params.conf: 标准代理配置
- security-headers.conf: 安全头配置

### templates/ - 配置模板
- project.tpl: 项目配置模板
- 支持占位符替换
- 快速生成新项目配置

## 🔄 迁移说明

从现有配置迁移到新架构：
1. 备份当前配置到backup/current-production/
2. 将现有配置拆分为模块化结构
3. 提取通用配置到snippets/
4. 使用新的管理脚本进行配置管理

## 🛡️ 安全考虑

- 配置文件权限设置为755
- SSL证书单独存储在ssl/目录
- 敏感信息不包含在配置文件中
- 支持配置验证和回滚

## 📚 参考文档

- nginx新配置.md: 详细的配置管理理论
- nginx.md: 新架构使用指南
- deploy-all/scripts/nginx-manager.sh: 现有管理脚本
