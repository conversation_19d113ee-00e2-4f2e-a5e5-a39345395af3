# Nginx配置管理使用指南

## 🎯 快速开始

### 重要提醒

**此目录现在是nginx的实际配置目录！**

- nginx主配置文件直接引用此目录：`include /root/workspace/shared/nginx/conf.d/*.conf;`
- 直接在此目录修改配置即可生效
- 无需同步到其他位置

## 📁 目录说明

```
/root/workspace/shared/nginx/
├── conf.d/          # 项目配置文件（nginx直接加载）
├── snippets/        # 可复用配置片段
├── templates/       # 配置模板
├── backup/          # 配置备份
└── scripts/         # 管理脚本
```

## 🔧 日常操作

### 1. 修改现有配置

```bash
# 编辑主配置文件
vim /root/workspace/shared/nginx/conf.d/liangliangdamowang.edu.deal.conf

# 测试配置语法
nginx -t

# 应用配置
systemctl reload nginx
```

### 2. 添加新项目

```bash
# 使用管理脚本添加项目
cd /root/workspace
./deploy-all/scripts/nginx-manager.sh --add-project example.com myproject project

# 或手动复制模板
cp shared/nginx/templates/project.tpl shared/nginx/conf.d/example.com.conf
# 然后编辑配置文件
```

### 3. 修改snippets

```bash
# 编辑SSL配置
vim /root/workspace/shared/nginx/snippets/ssl-params.conf

# 编辑安全头配置
vim /root/workspace/shared/nginx/snippets/security-headers.conf

# 测试并应用
nginx -t && systemctl reload nginx
```

## 🛡️ 安全操作流程

### 配置变更前

```bash
# 1. 创建备份
/root/workspace/shared/nginx/backup/backup-manager.sh create "before-change-$(date +%Y%m%d)"

# 2. 验证当前状态
nginx -t
systemctl status nginx
```

### 配置变更后

```bash
# 1. 测试语法
nginx -t

# 2. 如果测试通过，应用配置
systemctl reload nginx

# 3. 验证功能
curl -I https://liangliangdamowang.edu.deal/

# 4. 如果有问题，快速回滚
# 使用最新备份的restore.sh脚本
```

## 📋 常用命令

### nginx-manager.sh 脚本

```bash
# 查看系统状态
./deploy-all/scripts/nginx-manager.sh --status

# 创建备份
./deploy-all/scripts/nginx-manager.sh --backup

# 测试配置
./deploy-all/scripts/nginx-manager.sh --test

# 查看帮助
./deploy-all/scripts/nginx-manager.sh --help
```

### 备份管理

```bash
# 创建备份
/root/workspace/shared/nginx/backup/backup-manager.sh create "my-backup"

# 列出备份
/root/workspace/shared/nginx/backup/backup-manager.sh list

# 验证备份
/root/workspace/shared/nginx/backup/backup-manager.sh verify backup-id

# 恢复备份
/root/workspace/shared/nginx/backup/backup-manager.sh restore backup-id
```

### 配置测试

```bash
# 语法检查
nginx -t

# 查看完整配置
nginx -T

# 检查特定配置
nginx -T | grep -A 10 -B 10 "server_name"
```

## 🔍 故障排除

### 配置语法错误

```bash
# 查看详细错误信息
nginx -t

# 检查配置文件语法
nginx -T | head -50

# 如果有语法错误，检查最近的修改
```

### 服务无法启动

```bash
# 查看服务状态
systemctl status nginx

# 查看错误日志
tail -50 /var/log/nginx/error.log

# 快速回滚到最近的备份
cd /root/workspace/shared/nginx/backup/current-production/
./scripts/restore.sh
```

### 网站无法访问

```bash
# 检查nginx进程
ps aux | grep nginx

# 检查端口监听
netstat -tlnp | grep :80
netstat -tlnp | grep :443

# 检查防火墙
ufw status

# 测试本地访问
curl -I http://localhost/
curl -I https://localhost/
```

## 📝 最佳实践

### 1. 配置文件组织

- 使用有意义的文件名：`domain.com.conf`
- 添加注释说明配置用途
- 使用snippets避免重复配置
- 保持配置文件简洁易读

### 2. 变更管理

- 重要变更前先备份
- 使用版本控制管理配置
- 在测试环境先验证
- 记录变更原因和时间

### 3. 监控和维护

- 定期检查错误日志
- 监控SSL证书过期时间
- 定期清理旧备份
- 保持文档更新

## 🚨 紧急情况处理

### 快速回滚

```bash
# 方法1：使用最新备份
cd /root/workspace/shared/nginx/backup/current-production/
./scripts/restore.sh

# 方法2：使用特定备份
cd /root/workspace/shared/nginx/backup/production-YYYYMMDD-HHMMSS/
./scripts/restore.sh

# 验证回滚
nginx -t && systemctl reload nginx
```

### 紧急联系

如遇到无法解决的问题：
1. 立即执行回滚操作
2. 保存错误日志和配置文件
3. 记录问题发生的时间和操作

## 📚 参考资料

- [nginx.md](../nginx.md) - 完整架构文档
- [README.md](README.md) - 目录结构说明
- [备份系统文档](backup/README.md) - 备份系统详细说明
- [迁移报告](../nginx-migration-report.md) - 迁移过程记录

---

**最后更新**：2025-07-26  
**维护者**：Workspace项目团队
