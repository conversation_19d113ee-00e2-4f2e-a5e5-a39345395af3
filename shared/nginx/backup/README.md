# Nginx配置备份系统

完整的nginx配置备份和恢复系统，支持自动化备份、完整性验证、一键恢复等功能。

## 系统组件

### 核心脚本

- **`backup-manager.sh`** - 主备份管理器，提供完整的备份管理功能
- **`auto-backup.sh`** - 自动备份脚本，支持定时任务
- **`install-auto-backup.sh`** - 安装脚本，设置自动备份系统

### 配置文件

- **`backup-config.conf`** - 备份系统配置文件
- **`README.md`** - 本文档

## 功能特性

### 🔄 备份功能
- **完整备份**: nginx.conf、sites-available、sites-enabled、conf.d、snippets等
- **SSL信息备份**: 证书元数据、域名列表、过期信息
- **元数据记录**: 系统信息、备份清单、文件校验和
- **自动恢复脚本**: 每个备份都包含专用恢复脚本

### 🔍 验证功能
- **完整性验证**: SHA256校验和验证
- **结构检查**: 目录结构和关键文件检查
- **恢复脚本验证**: 确保恢复脚本可执行

### 📊 管理功能
- **备份列表**: 显示所有备份及状态
- **详细信息**: 查看备份的详细信息
- **备份比较**: 比较两个备份的差异
- **自动清理**: 保留指定数量的备份

### ⏰ 自动化功能
- **定时备份**: 支持每日、每周、每月自动备份
- **通知系统**: 邮件和Webhook通知
- **日志记录**: 详细的操作日志
- **锁机制**: 防止并发备份

## 快速开始

### 1. 安装自动备份系统

```bash
# 安装自动备份系统
sudo ./install-auto-backup.sh

# 或者只测试不安装
sudo ./install-auto-backup.sh test
```

### 2. 手动创建备份

```bash
# 创建普通备份
sudo ./backup-manager.sh create

# 创建命名备份
sudo ./backup-manager.sh create before-update

# 创建特定类型备份
sudo ./auto-backup.sh manual
```

### 3. 查看和管理备份

```bash
# 列出所有备份
./backup-manager.sh list

# 查看备份详细信息
./backup-manager.sh info <backup_id>

# 验证备份完整性
./backup-manager.sh verify <backup_id>

# 比较两个备份
./backup-manager.sh compare <backup_id1> <backup_id2>
```

### 4. 恢复备份

```bash
# 恢复指定备份
sudo ./backup-manager.sh restore <backup_id>

# 或使用备份专用恢复脚本
sudo /path/to/backup/scripts/restore.sh
```

## 备份目录结构

```
backup/
├── production-YYYYMMDD-HHMMSS/          # 备份目录
│   ├── nginx-config/                    # nginx配置文件
│   │   ├── main/nginx.conf              # 主配置文件
│   │   ├── sites-available/             # 可用站点
│   │   ├── sites-enabled/               # 启用站点
│   │   ├── conf.d/                      # 额外配置
│   │   └── snippets/                    # 配置片段
│   ├── ssl-info/                        # SSL证书信息
│   │   ├── certificates-list.txt        # 证书列表
│   │   ├── domains-list.txt             # 域名列表
│   │   └── expiry-info.txt              # 过期信息
│   ├── metadata/                        # 元数据
│   │   ├── system-info.txt              # 系统信息
│   │   ├── nginx-status.txt             # nginx状态
│   │   ├── backup-inventory.txt         # 备份清单
│   │   └── checksums.txt                # 文件校验和
│   └── scripts/                         # 脚本
│       └── restore.sh                   # 恢复脚本
├── current-production -> production-xxx  # 当前备份链接
└── backup-manager.sh                    # 管理脚本
```

## 定时备份

安装后会自动设置以下定时任务：

```bash
# 每日备份 - 凌晨2:00
0 2 * * * /path/to/auto-backup.sh daily

# 每周备份 - 周日凌晨3:00  
0 3 * * 0 /path/to/auto-backup.sh weekly

# 每月备份 - 每月1号凌晨4:00
0 4 1 * * /path/to/auto-backup.sh monthly
```

## 配置选项

编辑 `backup-config.conf` 自定义配置：

```bash
# 备份保留数量
MAX_BACKUPS=10

# 通知设置
BACKUP_EMAIL="<EMAIL>"
BACKUP_WEBHOOK="https://hooks.slack.com/..."

# 验证设置
VERIFY_AFTER_BACKUP=true
VERIFY_CHECKSUMS=true
```

## 日志和监控

### 查看日志

```bash
# 查看备份日志
tail -f /var/log/nginx-auto-backup.log

# 查看cron日志
grep nginx-auto-backup /var/log/syslog
```

### 监控备份状态

```bash
# 检查最近的备份
./backup-manager.sh list | head -5

# 验证最新备份
LATEST=$(./backup-manager.sh list | grep -v "备份ID" | head -1 | awk '{print $1}')
./backup-manager.sh verify $LATEST
```

## 故障排除

### 常见问题

1. **备份失败**
   ```bash
   # 检查nginx状态
   systemctl status nginx
   nginx -t
   
   # 检查磁盘空间
   df -h
   
   # 查看详细日志
   tail -50 /var/log/nginx-auto-backup.log
   ```

2. **恢复失败**
   ```bash
   # 验证备份完整性
   ./backup-manager.sh verify <backup_id>
   
   # 检查权限
   ls -la /etc/nginx/
   
   # 手动恢复
   sudo /path/to/backup/scripts/restore.sh
   ```

3. **定时任务不执行**
   ```bash
   # 检查cron服务
   systemctl status cron
   
   # 查看crontab
   crontab -l
   
   # 检查cron日志
   grep CRON /var/log/syslog
   ```

### 紧急恢复

如果系统出现问题，可以快速恢复：

```bash
# 1. 停止nginx
sudo systemctl stop nginx

# 2. 恢复最新备份
sudo ./backup-manager.sh restore $(./backup-manager.sh list | grep -v "备份ID" | head -1 | awk '{print $1}')

# 3. 测试配置
sudo nginx -t

# 4. 启动nginx
sudo systemctl start nginx
```

## 安全注意事项

1. **权限控制**: 备份文件包含敏感配置，确保适当的权限设置
2. **SSL证书**: 不备份实际证书文件，只备份元数据
3. **访问控制**: 限制备份目录的访问权限
4. **定期验证**: 定期验证备份完整性
5. **异地备份**: 考虑将备份复制到远程位置

## 集成说明

本备份系统与nginx-manager.sh完全集成：

```bash
# nginx-manager.sh中的备份功能
./nginx-manager.sh --batch-backup

# 直接使用备份管理器
./backup-manager.sh create
```

## 版本历史

- **v1.0** - 基础备份和恢复功能
- **v1.1** - 添加自动化和定时任务
- **v1.2** - 完整性验证和元数据
- **v1.3** - 通知系统和监控
- **v2.0** - 与nginx-manager.sh集成
