#!/bin/bash

# Nginx Auto Backup Script
# Automated backup script for nginx configurations
# Designed to be run via cron for regular backups

set -e

# Configuration
SCRIPT_DIR="$(dirname "$0")"
BACKUP_MANAGER="$SCRIPT_DIR/backup-manager.sh"
LOG_FILE="/var/log/nginx-auto-backup.log"
LOCK_FILE="/var/run/nginx-auto-backup.lock"
MAX_LOG_SIZE=10485760  # 10MB

# Logging function
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# Check if another backup is running
check_lock() {
    if [[ -f "$LOCK_FILE" ]]; then
        local pid=$(cat "$LOCK_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_message "WARNING" "Another backup process is running (PID: $pid)"
            exit 1
        else
            log_message "INFO" "Removing stale lock file"
            rm -f "$LOCK_FILE"
        fi
    fi
    
    # Create lock file
    echo $$ > "$LOCK_FILE"
}

# Remove lock file
cleanup() {
    rm -f "$LOCK_FILE"
}

# Trap to ensure cleanup
trap cleanup EXIT

# Rotate log file if too large
rotate_log() {
    if [[ -f "$LOG_FILE" && $(stat -c%s "$LOG_FILE") -gt $MAX_LOG_SIZE ]]; then
        mv "$LOG_FILE" "${LOG_FILE}.old"
        touch "$LOG_FILE"
        log_message "INFO" "Log file rotated"
    fi
}

# Check nginx status before backup
check_nginx_status() {
    if ! systemctl is-active --quiet nginx; then
        log_message "WARNING" "Nginx service is not running"
        return 1
    fi
    
    if ! nginx -t &>/dev/null; then
        log_message "ERROR" "Nginx configuration test failed"
        return 1
    fi
    
    return 0
}

# Perform backup
perform_backup() {
    local backup_type="$1"
    local backup_name=""
    
    case "$backup_type" in
        daily)
            backup_name="daily-auto"
            ;;
        weekly)
            backup_name="weekly-auto"
            ;;
        monthly)
            backup_name="monthly-auto"
            ;;
        manual)
            backup_name="manual-auto"
            ;;
        *)
            backup_name="auto"
            ;;
    esac
    
    log_message "INFO" "Starting $backup_type backup: $backup_name"
    
    # Check nginx status
    if ! check_nginx_status; then
        log_message "ERROR" "Nginx status check failed, aborting backup"
        return 1
    fi
    
    # Perform backup
    if "$BACKUP_MANAGER" create "$backup_name" >> "$LOG_FILE" 2>&1; then
        log_message "SUCCESS" "$backup_type backup completed successfully"
        
        # Get backup ID
        local backup_id=$(ls -t /root/workspace/shared/nginx/backup/production-${backup_name}-* 2>/dev/null | head -1 | xargs basename | sed "s/production-//")
        
        # Verify backup
        if "$BACKUP_MANAGER" verify "$backup_id" >> "$LOG_FILE" 2>&1; then
            log_message "SUCCESS" "Backup verification passed: $backup_id"
        else
            log_message "WARNING" "Backup verification failed: $backup_id"
        fi
        
        return 0
    else
        log_message "ERROR" "$backup_type backup failed"
        return 1
    fi
}

# Send notification (if configured)
send_notification() {
    local status="$1"
    local message="$2"
    
    # Email notification (if mail is configured)
    if command -v mail &>/dev/null && [[ -n "${BACKUP_EMAIL:-}" ]]; then
        echo "$message" | mail -s "Nginx Backup $status" "$BACKUP_EMAIL"
    fi
    
    # Webhook notification (if configured)
    if [[ -n "${BACKUP_WEBHOOK:-}" ]]; then
        curl -X POST "$BACKUP_WEBHOOK" \
             -H "Content-Type: application/json" \
             -d "{\"status\":\"$status\",\"message\":\"$message\",\"timestamp\":\"$(date -Iseconds)\"}" \
             &>/dev/null || true
    fi
}

# Main backup function
main() {
    local backup_type="${1:-auto}"
    
    # Initialize
    rotate_log
    check_lock
    
    log_message "INFO" "=== Nginx Auto Backup Started ==="
    log_message "INFO" "Backup type: $backup_type"
    log_message "INFO" "PID: $$"
    
    # Perform backup
    if perform_backup "$backup_type"; then
        local success_msg="Nginx $backup_type backup completed successfully"
        log_message "SUCCESS" "$success_msg"
        send_notification "SUCCESS" "$success_msg"
        exit 0
    else
        local error_msg="Nginx $backup_type backup failed"
        log_message "ERROR" "$error_msg"
        send_notification "FAILED" "$error_msg"
        exit 1
    fi
}

# Show help
show_help() {
    echo "Nginx自动备份脚本"
    echo
    echo "用法: $0 [backup_type]"
    echo
    echo "备份类型:"
    echo "  daily     每日备份"
    echo "  weekly    每周备份"
    echo "  monthly   每月备份"
    echo "  manual    手动备份"
    echo "  auto      自动备份 (默认)"
    echo
    echo "环境变量:"
    echo "  BACKUP_EMAIL    备份通知邮箱"
    echo "  BACKUP_WEBHOOK  备份通知webhook URL"
    echo
    echo "示例:"
    echo "  $0 daily          # 每日备份"
    echo "  $0 weekly         # 每周备份"
    echo "  $0                # 默认自动备份"
    echo
    echo "Cron示例:"
    echo "  # 每日凌晨2点备份"
    echo "  0 2 * * * $0 daily"
    echo "  # 每周日凌晨3点备份"
    echo "  0 3 * * 0 $0 weekly"
    echo "  # 每月1号凌晨4点备份"
    echo "  0 4 1 * * $0 monthly"
}

# Check arguments
case "${1:-auto}" in
    daily|weekly|monthly|manual|auto)
        main "$1"
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "错误: 未知的备份类型 '$1'"
        show_help
        exit 1
        ;;
esac
