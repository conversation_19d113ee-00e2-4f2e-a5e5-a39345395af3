# Nginx Backup Configuration
# Configuration file for nginx backup system

# Backup Settings
MAX_BACKUPS=10
BACKUP_PREFIX="production"
BACKUP_RETENTION_DAYS=30

# Directories
BACKUP_BASE_DIR="/root/workspace/shared/nginx/backup"
NGINX_CONFIG_DIR="/etc/nginx"
SSL_CERT_DIR="/etc/letsencrypt"

# Logging
LOG_FILE="/var/log/nginx-backup.log"
MAX_LOG_SIZE=10485760  # 10MB
LOG_LEVEL="INFO"  # DEBUG, INFO, WARNING, ERROR

# Notification Settings
# Email notifications (requires mail command)
BACKUP_EMAIL=""
EMAIL_ON_SUCCESS=false
EMAIL_ON_FAILURE=true

# Webhook notifications
BACKUP_WEBHOOK=""
WEBHOOK_ON_SUCCESS=false
WEBHOOK_ON_FAILURE=true

# Backup Verification
VERIFY_AFTER_BACKUP=true
VERIFY_CHECKSUMS=true
VERIFY_RESTORE_SCRIPT=true

# Cleanup Settings
AUTO_CLEANUP=true
CLEANUP_ON_BACKUP=true
KEEP_DAILY_BACKUPS=7
KEEP_WEEKLY_BACKUPS=4
KEEP_MONTHLY_BACKUPS=12

# Compression
COMPRESS_BACKUPS=false
COMPRESSION_LEVEL=6

# Security
BACKUP_PERMISSIONS=600
BACKUP_OWNER="root:root"

# Advanced Settings
PARALLEL_BACKUP=false
BACKUP_TIMEOUT=300  # 5 minutes
LOCK_TIMEOUT=600    # 10 minutes

# Backup Types Configuration
[daily]
enabled=true
schedule="0 2 * * *"
retention_days=7
compress=false

[weekly]
enabled=true
schedule="0 3 * * 0"
retention_days=28
compress=true

[monthly]
enabled=true
schedule="0 4 1 * *"
retention_days=365
compress=true

# Monitoring
HEALTH_CHECK_URL=""
HEALTH_CHECK_INTERVAL=300  # 5 minutes
HEALTH_CHECK_TIMEOUT=30

# Integration
INTEGRATE_WITH_NGINX_MANAGER=true
NGINX_MANAGER_PATH="/root/workspace/deploy-all/scripts/nginx-manager.sh"

# Custom Scripts
PRE_BACKUP_SCRIPT=""
POST_BACKUP_SCRIPT=""
PRE_RESTORE_SCRIPT=""
POST_RESTORE_SCRIPT=""
