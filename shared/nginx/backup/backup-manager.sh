#!/bin/bash

# Nginx Configuration Backup Manager
# Enhanced backup system for nginx configurations, SSL certificates, and metadata
# Supports automatic backup, restore, verification, and cleanup

set -e

# Source colors and logging functions
SCRIPT_DIR="$(dirname "$0")"
PROJECT_ROOT="/root/workspace"
source "$PROJECT_ROOT/deploy-all/common/colors.sh" 2>/dev/null || {
    # Fallback color definitions
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    CYAN='\033[0;36m'
    NC='\033[0m'
}

# Configuration
BACKUP_BASE_DIR="/root/workspace/shared/nginx/backup"
NGINX_CONFIG_DIR="/etc/nginx"
SSL_CERT_DIR="/etc/letsencrypt"
MAX_BACKUPS=10  # Keep last 10 backups
BACKUP_PREFIX="production"

# Logging functions
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_section() {
    echo -e "\n${CYAN}=== $1 ===${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本必须以root权限运行"
        exit 1
    fi
}

# Create backup directory structure
create_backup_structure() {
    local backup_dir="$1"
    
    mkdir -p "$backup_dir"/{nginx-config,ssl-info,metadata,scripts}
    mkdir -p "$backup_dir/nginx-config"/{main,sites-available,sites-enabled,conf.d,snippets,modules-available,modules-enabled}
}

# Backup nginx configuration files
backup_nginx_config() {
    local backup_dir="$1"
    local config_backup_dir="$backup_dir/nginx-config"
    
    log_info "备份nginx配置文件..."
    
    # Main configuration
    if [[ -f "$NGINX_CONFIG_DIR/nginx.conf" ]]; then
        cp "$NGINX_CONFIG_DIR/nginx.conf" "$config_backup_dir/main/"
        log_info "✓ 主配置文件已备份"
    fi
    
    # Sites configuration
    if [[ -d "$NGINX_CONFIG_DIR/sites-available" ]]; then
        cp -r "$NGINX_CONFIG_DIR/sites-available/"* "$config_backup_dir/sites-available/" 2>/dev/null || true
        log_info "✓ sites-available已备份"
    fi
    
    if [[ -d "$NGINX_CONFIG_DIR/sites-enabled" ]]; then
        # Backup symlinks as well
        find "$NGINX_CONFIG_DIR/sites-enabled" -type l -exec cp -P {} "$config_backup_dir/sites-enabled/" \; 2>/dev/null || true
        log_info "✓ sites-enabled已备份"
    fi
    
    # Additional configurations
    if [[ -d "$NGINX_CONFIG_DIR/conf.d" ]]; then
        cp -r "$NGINX_CONFIG_DIR/conf.d/"* "$config_backup_dir/conf.d/" 2>/dev/null || true
        log_info "✓ conf.d已备份"
    fi
    
    # Snippets (if using modular architecture)
    if [[ -d "$NGINX_CONFIG_DIR/snippets" ]]; then
        cp -r "$NGINX_CONFIG_DIR/snippets/"* "$config_backup_dir/snippets/" 2>/dev/null || true
        log_info "✓ snippets已备份"
    fi
    
    # Modules
    if [[ -d "$NGINX_CONFIG_DIR/modules-available" ]]; then
        cp -r "$NGINX_CONFIG_DIR/modules-available/"* "$config_backup_dir/modules-available/" 2>/dev/null || true
        log_info "✓ modules-available已备份"
    fi
    
    if [[ -d "$NGINX_CONFIG_DIR/modules-enabled" ]]; then
        find "$NGINX_CONFIG_DIR/modules-enabled" -type l -exec cp -P {} "$config_backup_dir/modules-enabled/" \; 2>/dev/null || true
        log_info "✓ modules-enabled已备份"
    fi
}

# Backup SSL certificate information
backup_ssl_info() {
    local backup_dir="$1"
    local ssl_backup_dir="$backup_dir/ssl-info"
    
    log_info "备份SSL证书信息..."
    
    # List all certificates
    if command -v certbot &> /dev/null; then
        certbot certificates > "$ssl_backup_dir/certificates-list.txt" 2>/dev/null || true
        log_info "✓ SSL证书列表已备份"
    fi
    
    # Certificate directories and metadata
    if [[ -d "$SSL_CERT_DIR/live" ]]; then
        # Don't backup actual certificates (security), just metadata
        find "$SSL_CERT_DIR/live" -name "*.pem" -exec ls -la {} \; > "$ssl_backup_dir/certificates-metadata.txt" 2>/dev/null || true
        
        # List domains
        find "$SSL_CERT_DIR/live" -maxdepth 1 -type d ! -name "live" -exec basename {} \; > "$ssl_backup_dir/domains-list.txt" 2>/dev/null || true
        log_info "✓ SSL证书元数据已备份"
    fi
    
    # Renewal configuration
    if [[ -d "$SSL_CERT_DIR/renewal" ]]; then
        cp -r "$SSL_CERT_DIR/renewal/"* "$ssl_backup_dir/" 2>/dev/null || true
        log_info "✓ SSL续期配置已备份"
    fi
    
    # Check certificate expiry
    if [[ -d "$SSL_CERT_DIR/live" ]]; then
        echo "# SSL Certificate Expiry Information" > "$ssl_backup_dir/expiry-info.txt"
        echo "# Generated: $(date)" >> "$ssl_backup_dir/expiry-info.txt"
        echo "" >> "$ssl_backup_dir/expiry-info.txt"
        
        for cert_dir in "$SSL_CERT_DIR/live"/*/; do
            if [[ -d "$cert_dir" && "$(basename "$cert_dir")" != "README" ]]; then
                local domain=$(basename "$cert_dir")
                local cert_file="$cert_dir/fullchain.pem"
                
                if [[ -f "$cert_file" ]]; then
                    local expiry_date=$(openssl x509 -enddate -noout -in "$cert_file" 2>/dev/null | cut -d= -f2)
                    local days_left=$(( ($(date -d "$expiry_date" +%s) - $(date +%s)) / 86400 )) 2>/dev/null || echo "unknown"
                    
                    echo "Domain: $domain" >> "$ssl_backup_dir/expiry-info.txt"
                    echo "  Expiry: $expiry_date" >> "$ssl_backup_dir/expiry-info.txt"
                    echo "  Days left: $days_left" >> "$ssl_backup_dir/expiry-info.txt"
                    echo "" >> "$ssl_backup_dir/expiry-info.txt"
                fi
            fi
        done
        log_info "✓ SSL证书过期信息已备份"
    fi
}

# Create backup metadata
create_backup_metadata() {
    local backup_dir="$1"
    local metadata_dir="$backup_dir/metadata"
    local timestamp="$2"
    
    log_info "创建备份元数据..."
    
    # System information
    cat > "$metadata_dir/system-info.txt" << EOF
# System Information at Backup Time
Backup Date: $(date)
Hostname: $(hostname)
OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d= -f2 | tr -d '"')
Nginx Version: $(nginx -v 2>&1)
Kernel: $(uname -r)
Uptime: $(uptime)

# Disk Usage
$(df -h)

# Memory Usage
$(free -h)

# Network Interfaces
$(ip addr show)
EOF
    
    # Nginx status
    cat > "$metadata_dir/nginx-status.txt" << EOF
# Nginx Status at Backup Time
Service Status: $(systemctl is-active nginx)
Service Enabled: $(systemctl is-enabled nginx)

# Configuration Test
$(nginx -t 2>&1)

# Process Information
$(ps aux | grep nginx | grep -v grep)

# Listening Ports
$(netstat -tlnp | grep nginx)
EOF
    
    # Backup inventory
    cat > "$metadata_dir/backup-inventory.txt" << EOF
# Backup Inventory
Backup ID: $timestamp
Created: $(date)
Backup Directory: $backup_dir

# File Counts
Nginx Config Files: $(find "$backup_dir/nginx-config" -type f | wc -l)
SSL Info Files: $(find "$backup_dir/ssl-info" -type f | wc -l)
Total Files: $(find "$backup_dir" -type f | wc -l)
Total Size: $(du -sh "$backup_dir" | cut -f1)

# Directory Structure
$(tree "$backup_dir" 2>/dev/null || find "$backup_dir" -type d | sort)
EOF
    
    # Create checksums for integrity verification
    log_info "生成文件校验和..."
    find "$backup_dir" -type f -not -path "*/metadata/*" -exec sha256sum {} \; > "$metadata_dir/checksums.txt"
    
    log_info "✓ 备份元数据已创建"
}

# Show help
show_help() {
    echo "Nginx配置备份管理器"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  create [name]     创建新备份 (可选自定义名称)"
    echo "  list              列出所有备份"
    echo "  restore <id>      恢复指定备份"
    echo "  verify <id>       验证备份完整性"
    echo "  cleanup           清理旧备份"
    echo "  info <id>         显示备份详细信息"
    echo "  compare <id1> <id2>  比较两个备份"
    echo "  help              显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 create                    # 创建新备份"
    echo "  $0 create before-update      # 创建命名备份"
    echo "  $0 list                      # 列出所有备份"
    echo "  $0 restore 20250726-120000   # 恢复指定备份"
    echo "  $0 verify 20250726-120000    # 验证备份完整性"
    echo "  $0 cleanup                   # 清理旧备份"
}

# Main backup function
create_backup() {
    local custom_name="$1"
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local backup_name="${BACKUP_PREFIX}-${timestamp}"
    
    if [[ -n "$custom_name" ]]; then
        backup_name="${BACKUP_PREFIX}-${custom_name}-${timestamp}"
    fi
    
    local backup_dir="$BACKUP_BASE_DIR/$backup_name"
    
    log_section "创建nginx配置备份: $backup_name"
    
    # Create directory structure
    create_backup_structure "$backup_dir"
    
    # Backup nginx configuration
    backup_nginx_config "$backup_dir"
    
    # Backup SSL information
    backup_ssl_info "$backup_dir"
    
    # Create metadata
    create_backup_metadata "$backup_dir" "$timestamp"
    
    # Create restore script
    create_restore_script "$backup_dir" "$backup_name"
    
    # Update current backup symlink
    ln -sfn "$backup_name" "$BACKUP_BASE_DIR/current-production"
    
    log_success "备份创建完成: $backup_dir"
    log_info "当前备份链接已更新: $BACKUP_BASE_DIR/current-production"
    
    # Auto cleanup old backups
    cleanup_old_backups
}

# Create restore script for this backup
create_restore_script() {
    local backup_dir="$1"
    local backup_name="$2"
    local restore_script="$backup_dir/scripts/restore.sh"
    
    cat > "$restore_script" << 'EOF'
#!/bin/bash

# Nginx Configuration Restore Script
# Auto-generated restore script for specific backup

set -e

BACKUP_DIR="$(dirname "$(dirname "$0")")"
BACKUP_NAME="$(basename "$BACKUP_DIR")"

echo "🔄 Starting nginx configuration restore from backup: $BACKUP_NAME"
echo "📁 Backup directory: $BACKUP_DIR"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo "❌ This script must be run as root"
   exit 1
fi

# Verify backup integrity first
echo "🔍 Verifying backup integrity..."
if [[ -f "$BACKUP_DIR/metadata/checksums.txt" ]]; then
    cd "$BACKUP_DIR"
    if sha256sum -c metadata/checksums.txt --quiet; then
        echo "✅ Backup integrity verified"
    else
        echo "❌ Backup integrity check failed"
        exit 1
    fi
else
    echo "⚠️  No checksums found, skipping integrity check"
fi

# Create safety backup of current configuration
echo "📦 Creating safety backup of current configuration..."
SAFETY_BACKUP="/tmp/nginx-safety-backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$SAFETY_BACKUP"
cp -r /etc/nginx/* "$SAFETY_BACKUP/" 2>/dev/null || true
echo "💾 Safety backup created: $SAFETY_BACKUP"

# Stop nginx service
echo "🛑 Stopping nginx service..."
systemctl stop nginx

# Restore function
restore_config() {
    echo "📄 Restoring nginx configuration..."
    
    # Main config
    if [[ -f "$BACKUP_DIR/nginx-config/main/nginx.conf" ]]; then
        cp "$BACKUP_DIR/nginx-config/main/nginx.conf" /etc/nginx/
        echo "✓ Main configuration restored"
    fi
    
    # Sites
    if [[ -d "$BACKUP_DIR/nginx-config/sites-available" ]]; then
        rm -rf /etc/nginx/sites-available/*
        cp -r "$BACKUP_DIR/nginx-config/sites-available/"* /etc/nginx/sites-available/ 2>/dev/null || true
        echo "✓ sites-available restored"
    fi
    
    if [[ -d "$BACKUP_DIR/nginx-config/sites-enabled" ]]; then
        rm -rf /etc/nginx/sites-enabled/*
        cp -r "$BACKUP_DIR/nginx-config/sites-enabled/"* /etc/nginx/sites-enabled/ 2>/dev/null || true
        echo "✓ sites-enabled restored"
    fi
    
    # Additional configs
    if [[ -d "$BACKUP_DIR/nginx-config/conf.d" ]]; then
        mkdir -p /etc/nginx/conf.d
        rm -rf /etc/nginx/conf.d/*
        cp -r "$BACKUP_DIR/nginx-config/conf.d/"* /etc/nginx/conf.d/ 2>/dev/null || true
        echo "✓ conf.d restored"
    fi
    
    # Snippets
    if [[ -d "$BACKUP_DIR/nginx-config/snippets" ]]; then
        mkdir -p /etc/nginx/snippets
        rm -rf /etc/nginx/snippets/*
        cp -r "$BACKUP_DIR/nginx-config/snippets/"* /etc/nginx/snippets/ 2>/dev/null || true
        echo "✓ snippets restored"
    fi
    
    # Modules
    if [[ -d "$BACKUP_DIR/nginx-config/modules-available" ]]; then
        mkdir -p /etc/nginx/modules-available
        rm -rf /etc/nginx/modules-available/*
        cp -r "$BACKUP_DIR/nginx-config/modules-available/"* /etc/nginx/modules-available/ 2>/dev/null || true
        echo "✓ modules-available restored"
    fi
    
    if [[ -d "$BACKUP_DIR/nginx-config/modules-enabled" ]]; then
        mkdir -p /etc/nginx/modules-enabled
        rm -rf /etc/nginx/modules-enabled/*
        cp -r "$BACKUP_DIR/nginx-config/modules-enabled/"* /etc/nginx/modules-enabled/ 2>/dev/null || true
        echo "✓ modules-enabled restored"
    fi
}

# Perform restore
restore_config

# Test nginx configuration
echo "🧪 Testing nginx configuration..."
if nginx -t; then
    echo "✅ Nginx configuration test passed"
    
    # Start nginx service
    echo "🚀 Starting nginx service..."
    systemctl start nginx
    
    # Check service status
    if systemctl is-active --quiet nginx; then
        echo "✅ Nginx service started successfully"
        echo "🎉 Configuration restore completed successfully!"
        echo "📋 Safety backup saved to: $SAFETY_BACKUP"
        
        # Show service status
        echo ""
        echo "📊 Service Status:"
        systemctl status nginx --no-pager -l
        
        # Test connectivity
        echo ""
        echo "🌐 Testing connectivity:"
        curl -I http://localhost/ 2>/dev/null | head -1 || echo "❌ HTTP test failed"
        curl -I https://localhost/ 2>/dev/null | head -1 || echo "❌ HTTPS test failed"
        
    else
        echo "❌ Failed to start nginx service"
        echo "🔄 Restoring from safety backup..."
        cp -r "$SAFETY_BACKUP/"* /etc/nginx/
        systemctl start nginx
        exit 1
    fi
else
    echo "❌ Nginx configuration test failed"
    echo "🔄 Restoring from safety backup..."
    cp -r "$SAFETY_BACKUP/"* /etc/nginx/
    systemctl start nginx
    exit 1
fi

echo ""
echo "✅ Restore completed successfully!"
echo "📁 Backup used: $BACKUP_NAME"
echo "💾 Safety backup: $SAFETY_BACKUP"
EOF

    chmod +x "$restore_script"
    log_info "✓ 恢复脚本已创建: $restore_script"
}

# Cleanup old backups
cleanup_old_backups() {
    log_info "清理旧备份..."
    
    local backup_count=$(find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "${BACKUP_PREFIX}-*" | wc -l)
    
    if [[ $backup_count -gt $MAX_BACKUPS ]]; then
        local to_remove=$((backup_count - MAX_BACKUPS))
        log_info "发现 $backup_count 个备份，需要删除最旧的 $to_remove 个"
        
        # Remove oldest backups
        find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "${BACKUP_PREFIX}-*" -printf '%T@ %p\n' | \
        sort -n | head -n $to_remove | cut -d' ' -f2- | \
        while read -r old_backup; do
            log_info "删除旧备份: $(basename "$old_backup")"
            rm -rf "$old_backup"
        done
        
        log_success "清理完成，保留最新的 $MAX_BACKUPS 个备份"
    else
        log_info "当前有 $backup_count 个备份，无需清理"
    fi
}

# List all backups
list_backups() {
    log_section "nginx配置备份列表"

    if [[ ! -d "$BACKUP_BASE_DIR" ]]; then
        log_warning "备份目录不存在: $BACKUP_BASE_DIR"
        return 1
    fi

    local backups=($(find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "${BACKUP_PREFIX}-*" | sort -r))

    if [[ ${#backups[@]} -eq 0 ]]; then
        log_info "未找到任何备份"
        return 0
    fi

    echo -e "${CYAN}📋 找到 ${#backups[@]} 个备份：${NC}"
    echo

    printf "%-25s %-15s %-10s %-20s %s\n" "备份ID" "创建时间" "大小" "状态" "描述"
    printf "%-25s %-15s %-10s %-20s %s\n" "$(printf '%*s' 25 | tr ' ' '-')" "$(printf '%*s' 15 | tr ' ' '-')" "$(printf '%*s' 10 | tr ' ' '-')" "$(printf '%*s' 20 | tr ' ' '-')" "$(printf '%*s' 20 | tr ' ' '-')"

    for backup_dir in "${backups[@]}"; do
        local backup_name=$(basename "$backup_dir")
        local backup_id=${backup_name#${BACKUP_PREFIX}-}

        # Get creation time
        local create_time=""
        if [[ -f "$backup_dir/metadata/backup-inventory.txt" ]]; then
            create_time=$(grep "Created:" "$backup_dir/metadata/backup-inventory.txt" 2>/dev/null | cut -d: -f2- | xargs)
        fi
        [[ -z "$create_time" ]] && create_time=$(stat -c %y "$backup_dir" | cut -d. -f1)

        # Get size
        local size=$(du -sh "$backup_dir" 2>/dev/null | cut -f1)

        # Check status
        local status="✅ 完整"
        if [[ ! -f "$backup_dir/metadata/checksums.txt" ]]; then
            status="⚠️  无校验"
        elif [[ ! -f "$backup_dir/scripts/restore.sh" ]]; then
            status="⚠️  无恢复脚本"
        fi

        # Check if current
        local description=""
        if [[ -L "$BACKUP_BASE_DIR/current-production" ]]; then
            local current_target=$(readlink "$BACKUP_BASE_DIR/current-production")
            if [[ "$current_target" == "$backup_name" ]]; then
                description="[当前备份]"
            fi
        fi

        printf "%-25s %-15s %-10s %-20s %s\n" "$backup_id" "${create_time:0:16}" "$size" "$status" "$description"
    done

    echo
    log_info "使用 '$0 info <backup_id>' 查看详细信息"
    log_info "使用 '$0 restore <backup_id>' 恢复备份"
}

# Verify backup integrity
verify_backup() {
    local backup_id="$1"
    local backup_dir="$BACKUP_BASE_DIR/${BACKUP_PREFIX}-${backup_id}"

    log_section "验证备份完整性: $backup_id"

    if [[ ! -d "$backup_dir" ]]; then
        log_error "备份不存在: $backup_id"
        return 1
    fi

    local errors=0

    # Check directory structure
    log_info "检查目录结构..."
    local required_dirs=("nginx-config" "ssl-info" "metadata" "scripts")
    for dir in "${required_dirs[@]}"; do
        if [[ -d "$backup_dir/$dir" ]]; then
            echo "  ✓ $dir/"
        else
            echo "  ❌ $dir/ (缺失)"
            errors=$((errors + 1))
        fi
    done

    # Check essential files
    log_info "检查关键文件..."
    local required_files=(
        "nginx-config/main/nginx.conf"
        "metadata/backup-inventory.txt"
        "scripts/restore.sh"
    )

    for file in "${required_files[@]}"; do
        if [[ -f "$backup_dir/$file" ]]; then
            echo "  ✓ $file"
        else
            echo "  ❌ $file (缺失)"
            errors=$((errors + 1))
        fi
    done

    # Verify checksums
    log_info "验证文件完整性..."
    if [[ -f "$backup_dir/metadata/checksums.txt" ]]; then
        cd "$backup_dir"
        if sha256sum -c metadata/checksums.txt --quiet 2>/dev/null; then
            echo "  ✅ 所有文件校验和正确"
        else
            echo "  ❌ 文件校验和验证失败"
            errors=$((errors + 1))
        fi
    else
        echo "  ⚠️  无校验和文件"
        errors=$((errors + 1))
    fi

    # Check restore script
    log_info "检查恢复脚本..."
    if [[ -x "$backup_dir/scripts/restore.sh" ]]; then
        echo "  ✓ 恢复脚本可执行"
    else
        echo "  ❌ 恢复脚本不可执行"
        errors=$((errors + 1))
    fi

    # Summary
    echo
    if [[ $errors -eq 0 ]]; then
        log_success "备份验证通过，所有检查项目正常"
        return 0
    else
        log_error "备份验证失败，发现 $errors 个问题"
        return 1
    fi
}

# Show detailed backup information
show_backup_info() {
    local backup_id="$1"
    local backup_dir="$BACKUP_BASE_DIR/${BACKUP_PREFIX}-${backup_id}"

    log_section "备份详细信息: $backup_id"

    if [[ ! -d "$backup_dir" ]]; then
        log_error "备份不存在: $backup_id"
        return 1
    fi

    # Basic information
    echo -e "${CYAN}📋 基本信息：${NC}"
    echo "  • 备份ID: $backup_id"
    echo "  • 备份路径: $backup_dir"
    echo "  • 创建时间: $(stat -c %y "$backup_dir" | cut -d. -f1)"
    echo "  • 总大小: $(du -sh "$backup_dir" | cut -f1)"

    # Check if current backup
    if [[ -L "$BACKUP_BASE_DIR/current-production" ]]; then
        local current_target=$(readlink "$BACKUP_BASE_DIR/current-production")
        if [[ "$current_target" == "${BACKUP_PREFIX}-${backup_id}" ]]; then
            echo "  • 状态: 🟢 当前生产备份"
        else
            echo "  • 状态: 🔵 历史备份"
        fi
    fi

    # Inventory information
    if [[ -f "$backup_dir/metadata/backup-inventory.txt" ]]; then
        echo
        echo -e "${CYAN}📦 备份清单：${NC}"
        grep -E "(Nginx Config Files|SSL Info Files|Total Files|Total Size)" "$backup_dir/metadata/backup-inventory.txt" | sed 's/^/  • /'
    fi

    # System information
    if [[ -f "$backup_dir/metadata/system-info.txt" ]]; then
        echo
        echo -e "${CYAN}🖥️  系统信息：${NC}"
        grep -E "(Backup Date|Hostname|OS|Nginx Version)" "$backup_dir/metadata/system-info.txt" | sed 's/^/  • /'
    fi

    # SSL certificates
    if [[ -f "$backup_dir/ssl-info/domains-list.txt" ]]; then
        echo
        echo -e "${CYAN}🔒 SSL证书：${NC}"
        local cert_count=$(wc -l < "$backup_dir/ssl-info/domains-list.txt")
        echo "  • 证书数量: $cert_count"
        echo "  • 域名列表:"
        cat "$backup_dir/ssl-info/domains-list.txt" | sed 's/^/    - /'
    fi

    # Configuration files
    echo
    echo -e "${CYAN}⚙️  配置文件：${NC}"
    local config_files=$(find "$backup_dir/nginx-config" -type f -name "*.conf" | wc -l)
    echo "  • 配置文件数量: $config_files"

    if [[ -d "$backup_dir/nginx-config/sites-available" ]]; then
        local sites_count=$(find "$backup_dir/nginx-config/sites-available" -type f | wc -l)
        echo "  • 站点配置: $sites_count 个"
    fi

    if [[ -d "$backup_dir/nginx-config/conf.d" ]]; then
        local confd_count=$(find "$backup_dir/nginx-config/conf.d" -type f -name "*.conf" | wc -l)
        echo "  • conf.d配置: $confd_count 个"
    fi

    # Restore information
    echo
    echo -e "${CYAN}🔄 恢复信息：${NC}"
    if [[ -x "$backup_dir/scripts/restore.sh" ]]; then
        echo "  • 恢复脚本: ✅ 可用"
        echo "  • 恢复命令: $backup_dir/scripts/restore.sh"
    else
        echo "  • 恢复脚本: ❌ 不可用"
    fi

    echo "  • 手动恢复: $0 restore $backup_id"
}

# Restore backup
restore_backup() {
    local backup_id="$1"
    local backup_dir="$BACKUP_BASE_DIR/${BACKUP_PREFIX}-${backup_id}"

    log_section "恢复nginx配置: $backup_id"

    if [[ ! -d "$backup_dir" ]]; then
        log_error "备份不存在: $backup_id"
        return 1
    fi

    # Verify backup first
    log_info "验证备份完整性..."
    if ! verify_backup "$backup_id"; then
        log_error "备份验证失败，无法恢复"
        return 1
    fi

    # Check if restore script exists
    if [[ -x "$backup_dir/scripts/restore.sh" ]]; then
        log_info "使用备份专用恢复脚本..."
        "$backup_dir/scripts/restore.sh"
    else
        log_warning "备份专用恢复脚本不存在，使用通用恢复流程"
        # Implement generic restore logic here if needed
        log_error "通用恢复功能尚未实现，请使用备份专用恢复脚本"
        return 1
    fi
}

# Compare two backups
compare_backups() {
    local backup_id1="$1"
    local backup_id2="$2"
    local backup_dir1="$BACKUP_BASE_DIR/${BACKUP_PREFIX}-${backup_id1}"
    local backup_dir2="$BACKUP_BASE_DIR/${BACKUP_PREFIX}-${backup_id2}"

    log_section "比较备份: $backup_id1 vs $backup_id2"

    # Check if both backups exist
    if [[ ! -d "$backup_dir1" ]]; then
        log_error "备份不存在: $backup_id1"
        return 1
    fi

    if [[ ! -d "$backup_dir2" ]]; then
        log_error "备份不存在: $backup_id2"
        return 1
    fi

    echo -e "${CYAN}📊 备份比较结果：${NC}"

    # Compare sizes
    local size1=$(du -sb "$backup_dir1" | cut -f1)
    local size2=$(du -sb "$backup_dir2" | cut -f1)
    local size_diff=$((size2 - size1))

    echo "  • 大小比较:"
    echo "    - $backup_id1: $(du -sh "$backup_dir1" | cut -f1)"
    echo "    - $backup_id2: $(du -sh "$backup_dir2" | cut -f1)"
    if [[ $size_diff -gt 0 ]]; then
        echo "    - 差异: +$(numfmt --to=iec $size_diff) (较新备份更大)"
    elif [[ $size_diff -lt 0 ]]; then
        echo "    - 差异: $(numfmt --to=iec $size_diff) (较新备份更小)"
    else
        echo "    - 差异: 相同大小"
    fi

    # Compare file counts
    local files1=$(find "$backup_dir1" -type f | wc -l)
    local files2=$(find "$backup_dir2" -type f | wc -l)

    echo "  • 文件数量:"
    echo "    - $backup_id1: $files1 个文件"
    echo "    - $backup_id2: $files2 个文件"
    echo "    - 差异: $((files2 - files1)) 个文件"

    # Compare main nginx.conf
    echo "  • 主配置文件:"
    if [[ -f "$backup_dir1/nginx-config/main/nginx.conf" && -f "$backup_dir2/nginx-config/main/nginx.conf" ]]; then
        if diff -q "$backup_dir1/nginx-config/main/nginx.conf" "$backup_dir2/nginx-config/main/nginx.conf" >/dev/null; then
            echo "    - nginx.conf: ✅ 相同"
        else
            echo "    - nginx.conf: ❌ 不同"
            echo "    - 使用 'diff $backup_dir1/nginx-config/main/nginx.conf $backup_dir2/nginx-config/main/nginx.conf' 查看差异"
        fi
    else
        echo "    - nginx.conf: ⚠️  某个备份中缺失"
    fi

    # Compare SSL certificates
    echo "  • SSL证书:"
    if [[ -f "$backup_dir1/ssl-info/domains-list.txt" && -f "$backup_dir2/ssl-info/domains-list.txt" ]]; then
        local domains1=$(wc -l < "$backup_dir1/ssl-info/domains-list.txt")
        local domains2=$(wc -l < "$backup_dir2/ssl-info/domains-list.txt")
        echo "    - 证书数量: $domains1 vs $domains2"

        if diff -q "$backup_dir1/ssl-info/domains-list.txt" "$backup_dir2/ssl-info/domains-list.txt" >/dev/null; then
            echo "    - 域名列表: ✅ 相同"
        else
            echo "    - 域名列表: ❌ 不同"
        fi
    else
        echo "    - SSL信息: ⚠️  某个备份中缺失"
    fi

    log_info "详细比较请使用: diff -r $backup_dir1 $backup_dir2"
}

# Main script logic
main() {
    case "${1:-help}" in
        create)
            check_root
            create_backup "$2"
            ;;
        list)
            list_backups
            ;;
        restore)
            if [[ -z "$2" ]]; then
                log_error "请指定要恢复的备份ID"
                exit 1
            fi
            check_root
            restore_backup "$2"
            ;;
        verify)
            if [[ -z "$2" ]]; then
                log_error "请指定要验证的备份ID"
                exit 1
            fi
            verify_backup "$2"
            ;;
        cleanup)
            check_root
            cleanup_old_backups
            ;;
        info)
            if [[ -z "$2" ]]; then
                log_error "请指定要查看的备份ID"
                exit 1
            fi
            show_backup_info "$2"
            ;;
        compare)
            if [[ -z "$2" || -z "$3" ]]; then
                log_error "请指定两个要比较的备份ID"
                exit 1
            fi
            compare_backups "$2" "$3"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
