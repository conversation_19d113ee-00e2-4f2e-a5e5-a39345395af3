#!/bin/bash

# Nginx Auto Backup Installation Script
# Sets up automatic nginx configuration backups

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(dirname "$0")"
BACKUP_SCRIPTS_DIR="$SCRIPT_DIR"
LOG_DIR="/var/log"
CRON_USER="root"

# Logging functions
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_section() {
    echo -e "\n${CYAN}=== $1 ===${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本必须以root权限运行"
        exit 1
    fi
}

# Check dependencies
check_dependencies() {
    log_section "检查依赖"
    
    local missing_deps=()
    
    # Check required commands
    local required_commands=("nginx" "systemctl" "crontab" "sha256sum")
    for cmd in "${required_commands[@]}"; do
        if command -v "$cmd" &>/dev/null; then
            log_info "✓ $cmd 已安装"
        else
            log_error "✗ $cmd 未安装"
            missing_deps+=("$cmd")
        fi
    done
    
    # Check optional commands
    local optional_commands=("mail" "curl" "tree")
    for cmd in "${optional_commands[@]}"; do
        if command -v "$cmd" &>/dev/null; then
            log_info "✓ $cmd 已安装 (可选)"
        else
            log_warning "✗ $cmd 未安装 (可选，用于通知功能)"
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少必需的依赖: ${missing_deps[*]}"
        exit 1
    fi
    
    log_success "所有必需依赖已满足"
}

# Setup log directory and permissions
setup_logging() {
    log_section "设置日志"
    
    # Create log file
    local log_file="/var/log/nginx-auto-backup.log"
    touch "$log_file"
    chmod 644 "$log_file"
    chown root:root "$log_file"
    
    log_info "✓ 日志文件已创建: $log_file"
    
    # Setup logrotate
    cat > /etc/logrotate.d/nginx-auto-backup << 'EOF'
/var/log/nginx-auto-backup.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        # Send signal to reload if process is running
        pkill -USR1 nginx-auto-backup 2>/dev/null || true
    endscript
}
EOF
    
    log_info "✓ Logrotate配置已创建"
}

# Set script permissions
setup_permissions() {
    log_section "设置权限"
    
    # Make scripts executable
    chmod +x "$BACKUP_SCRIPTS_DIR/backup-manager.sh"
    chmod +x "$BACKUP_SCRIPTS_DIR/auto-backup.sh"
    
    # Set ownership
    chown root:root "$BACKUP_SCRIPTS_DIR"/*.sh
    chown root:root "$BACKUP_SCRIPTS_DIR"/*.conf
    
    log_info "✓ 脚本权限已设置"
    
    # Create backup directory if not exists
    local backup_dir="/root/workspace/shared/nginx/backup"
    if [[ ! -d "$backup_dir" ]]; then
        mkdir -p "$backup_dir"
        chmod 700 "$backup_dir"
        chown root:root "$backup_dir"
        log_info "✓ 备份目录已创建: $backup_dir"
    fi
}

# Setup cron jobs
setup_cron() {
    log_section "设置定时任务"
    
    local auto_backup_script="$BACKUP_SCRIPTS_DIR/auto-backup.sh"
    
    # Check if cron service is running
    if ! systemctl is-active --quiet cron; then
        log_info "启动cron服务..."
        systemctl start cron
        systemctl enable cron
    fi
    
    # Backup existing crontab
    local cron_backup="/tmp/crontab-backup-$(date +%Y%m%d-%H%M%S)"
    crontab -l > "$cron_backup" 2>/dev/null || touch "$cron_backup"
    log_info "✓ 现有crontab已备份: $cron_backup"
    
    # Remove existing nginx backup cron jobs
    crontab -l 2>/dev/null | grep -v "nginx.*backup" | crontab - 2>/dev/null || true
    
    # Add new cron jobs
    (crontab -l 2>/dev/null; cat << EOF

# Nginx Auto Backup Jobs - Added by install script
# Daily backup at 2:00 AM
0 2 * * * $auto_backup_script daily >/dev/null 2>&1

# Weekly backup at 3:00 AM on Sunday
0 3 * * 0 $auto_backup_script weekly >/dev/null 2>&1

# Monthly backup at 4:00 AM on 1st day of month
0 4 1 * * $auto_backup_script monthly >/dev/null 2>&1

EOF
    ) | crontab -
    
    log_success "✓ 定时任务已添加"
    
    # Show current crontab
    echo
    log_info "当前定时任务:"
    crontab -l | grep -A 10 -B 2 "nginx.*backup" || log_warning "未找到nginx备份任务"
}

# Test backup system
test_backup() {
    log_section "测试备份系统"
    
    log_info "执行测试备份..."
    if "$BACKUP_SCRIPTS_DIR/auto-backup.sh" manual; then
        log_success "✓ 测试备份成功"
        
        # List backups
        log_info "当前备份列表:"
        "$BACKUP_SCRIPTS_DIR/backup-manager.sh" list
        
    else
        log_error "✗ 测试备份失败"
        return 1
    fi
}

# Show installation summary
show_summary() {
    log_section "安装完成"
    
    echo -e "${GREEN}🎉 Nginx自动备份系统安装完成！${NC}"
    echo
    echo -e "${CYAN}📋 安装摘要：${NC}"
    echo "  • 备份管理器: $BACKUP_SCRIPTS_DIR/backup-manager.sh"
    echo "  • 自动备份脚本: $BACKUP_SCRIPTS_DIR/auto-backup.sh"
    echo "  • 配置文件: $BACKUP_SCRIPTS_DIR/backup-config.conf"
    echo "  • 日志文件: /var/log/nginx-auto-backup.log"
    echo "  • 备份目录: /root/workspace/shared/nginx/backup"
    echo
    echo -e "${CYAN}⏰ 定时任务：${NC}"
    echo "  • 每日备份: 凌晨2:00"
    echo "  • 每周备份: 周日凌晨3:00"
    echo "  • 每月备份: 每月1号凌晨4:00"
    echo
    echo -e "${CYAN}🔧 常用命令：${NC}"
    echo "  • 手动备份: $BACKUP_SCRIPTS_DIR/auto-backup.sh manual"
    echo "  • 列出备份: $BACKUP_SCRIPTS_DIR/backup-manager.sh list"
    echo "  • 查看备份: $BACKUP_SCRIPTS_DIR/backup-manager.sh info <backup_id>"
    echo "  • 恢复备份: $BACKUP_SCRIPTS_DIR/backup-manager.sh restore <backup_id>"
    echo "  • 验证备份: $BACKUP_SCRIPTS_DIR/backup-manager.sh verify <backup_id>"
    echo
    echo -e "${CYAN}📝 日志查看：${NC}"
    echo "  • 查看日志: tail -f /var/log/nginx-auto-backup.log"
    echo "  • 查看cron日志: grep nginx-auto-backup /var/log/syslog"
    echo
    echo -e "${YELLOW}💡 提示：${NC}"
    echo "  • 可以编辑 $BACKUP_SCRIPTS_DIR/backup-config.conf 自定义配置"
    echo "  • 建议定期检查备份日志确保系统正常运行"
    echo "  • 可以设置邮件通知，编辑配置文件中的BACKUP_EMAIL"
}

# Uninstall function
uninstall() {
    log_section "卸载自动备份系统"
    
    # Remove cron jobs
    crontab -l 2>/dev/null | grep -v "nginx.*backup" | crontab - 2>/dev/null || true
    log_info "✓ 定时任务已移除"
    
    # Remove logrotate config
    rm -f /etc/logrotate.d/nginx-auto-backup
    log_info "✓ Logrotate配置已移除"
    
    # Note: Keep backup files and scripts for safety
    log_warning "备份文件和脚本已保留，如需完全删除请手动操作"
    
    log_success "自动备份系统已卸载"
}

# Main installation function
main() {
    case "${1:-install}" in
        install)
            log_section "安装Nginx自动备份系统"
            check_root
            check_dependencies
            setup_logging
            setup_permissions
            setup_cron
            test_backup
            show_summary
            ;;
        uninstall)
            check_root
            uninstall
            ;;
        test)
            check_root
            test_backup
            ;;
        help|--help|-h)
            echo "Nginx自动备份安装脚本"
            echo
            echo "用法: $0 [选项]"
            echo
            echo "选项:"
            echo "  install     安装自动备份系统 (默认)"
            echo "  uninstall   卸载自动备份系统"
            echo "  test        测试备份系统"
            echo "  help        显示此帮助信息"
            ;;
        *)
            log_error "未知选项: $1"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
