# Unified Nginx configuration for liangliangdamowang.edu.deal
# Generated by Deploy-All Nginx Manager
# Fri Jul 25 02:25:22 UTC 2025

# Upstream definitions
upstream new_api_backend {
    server 127.0.0.1:3000;
    keepalive 32;
}

upstream love_backend {
    server 127.0.0.1:1314;
    keepalive 32;
}

# HTTP Server Block
server {
    listen 80;
    server_name liangliangdamowang.edu.deal;

    # Redirect all HTTP traffic to HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS Server Block
server {
    listen 443 ssl http2;
    server_name liangliangdamowang.edu.deal;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/liangliangdamowang.edu.deal/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/liangliangdamowang.edu.deal/privkey.pem;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # ===== LOVE SITE CONFIGURATIONS =====

    # Love site main page
    location = /love/ {
        alias /root/workspace/love/html/;
        try_files /index.html =404;
    }

    location = /love {
        return 301 $scheme://$host/love/;
    }

    # Love site clean URLs (支持多页面扩展)
    location ~ ^/love/(together-days|anniversary|meetings|memorial|future-page1|future-page2)$ {
        alias /root/workspace/love/html/;
        try_files /$1.html =404;
    }

    # Love site static files (CSS/JS)
    location ~ ^/love/(style\.css|pages\.css|script\.js|romantic-quotes\.js)$ {
        alias /root/workspace/love/$1;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Love site background files
    location /love/background/ {
        alias /root/workspace/love/background/;
        try_files $uri $uri/ =404;

        location ~* \.(mp4|webm|ogg|avi|mov|wmv|flv|mkv)$ {
            add_header Accept-Ranges bytes;
            add_header Cache-Control "public, max-age=31536000";
            expires 1y;
        }
    }

    # Love site fonts
    location /love/fonts/ {
        alias /root/workspace/love/fonts/;
        try_files $uri =404;

        location ~* \.(ttf|otf|woff|woff2|eot)$ {
            add_header Access-Control-Allow-Origin "*";
            expires 1y;
        }
    }

    # Love site API routes
    location /love/api/ {
        rewrite ^/love/api/(.*) /api/$1 break;
        proxy_pass http://love_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # CORS headers
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
    }

    # ===== NEW-API CONFIGURATIONS =====

    # New-API service at /new-api path (修复路径重定向问题)
    location /new-api/ {
        # 使用rewrite去掉/new-api前缀，因为应用内部不需要这个前缀
        rewrite ^/new-api/(.*) /$1 break;
        proxy_pass http://new_api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Forwarded-Prefix /new-api;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # Buffer settings for streaming responses
        proxy_buffering off;
        proxy_cache off;

        # CORS headers for API requests
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
    }

    # Redirect /new-api to /new-api/
    location = /new-api {
        return 301 $scheme://$host/new-api/;
    }

    # New-API static assets (处理前端资源路径问题)
    # 这些资源请求来自 /new-api/ 页面，但路径是绝对路径，需要重写为子路径
    location ~ ^/(assets/.*|logo\.png|favicon\.ico)$ {
        # 重写路径为 /new-api/ 前缀，然后代理到后端
        rewrite ^/(.*) /new-api/$1 break;
        proxy_pass http://new_api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Forwarded-Prefix /new-api;

        # 缓存静态资源
        expires 1y;
        add_header Cache-Control "public, immutable";

        # CORS headers for static assets
        add_header 'Access-Control-Allow-Origin' '*' always;
    }

    # ===== ROOT CONFIGURATIONS =====

    # Test page for debugging
    location = /test {
        alias /var/www/html/test_new_api.html;
        add_header Content-Type text/html;
    }

    # Welcome page for root path
    location = / {
        return 200 '<!DOCTYPE html>
<html>
<head>
    <title>Welcome to liangliangdamowang.edu.deal</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .container { max-width: 600px; margin: 0 auto; background: rgba(255,255,255,0.1); padding: 40px; border-radius: 15px; backdrop-filter: blur(10px); }
        .service-link { display: inline-block; margin: 20px; padding: 15px 30px;
                       background: rgba(255,255,255,0.2); color: white; text-decoration: none;
                       border-radius: 25px; border: 2px solid rgba(255,255,255,0.3); transition: all 0.3s; }
        .service-link:hover { background: rgba(255,255,255,0.3); transform: translateY(-2px); }
        h1 { margin-bottom: 30px; font-size: 2.5em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Welcome to Our Platform</h1>
        <p>Choose a service to access:</p>
        <a href="/new-api/" class="service-link">🚀 New-API Platform</a>
        <a href="/love/" class="service-link">💕 Love Website</a>
    </div>
</body>
</html>';
        add_header Content-Type text/html;
    }

    # Fallback for other root paths - redirect to welcome page
    location / {
        return 301 $scheme://$host/;
    }
}
