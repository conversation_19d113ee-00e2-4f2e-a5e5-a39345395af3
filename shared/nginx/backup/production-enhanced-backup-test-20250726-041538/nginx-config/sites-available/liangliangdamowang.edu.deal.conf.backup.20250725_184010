# Complete Nginx configuration for liangliangdamowang.edu.deal
# This configuration includes both New-API and Love site
# Generated by Love site management script

# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name liangliangdamowang.edu.deal;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

# Upstream for love site backend
upstream love_backend {
    server 127.0.0.1:1314;
    keepalive 32;
}

# Upstream for new-api backend (Docker container)
upstream new_api_backend {
    server **********:3000;
    keepalive 32;
}

# HTTPS server
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name liangliangdamowang.edu.deal;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/liangliangdamowang.edu.deal/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/liangliangdamowang.edu.deal/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # ===== LOVE SITE CONFIGURATIONS =====
    
    # Handle /love without trailing slash
    location = /love {
        return 301 $scheme://$host/love/;
    }
    
    # Love site main page
    location = /love/ {
        alias /root/workspace/love/html/;
        try_files /index.html =404;
    }

    # Love site clean URLs (without .html extension)
    location ~ ^/love/(together-days|anniversary|meetings|memorial)$ {
        alias /root/workspace/love/html/;
        try_files /$1.html =404;
    }

    # Love site HTML files (direct access)
    location /love/html/ {
        alias /root/workspace/love/html/;
        try_files $uri $uri/ =404;

        # Cache static assets
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Love site root files (CSS, JS, etc.)
    location /love/style.css {
        alias /root/workspace/love/style.css;
    }

    location /love/pages.css {
        alias /root/workspace/love/pages.css;
    }

    location /love/script.js {
        alias /root/workspace/love/script.js;
    }

    location /love/romantic-quotes.js {
        alias /root/workspace/love/romantic-quotes.js;
    }

    # Love site fonts
    location /love/fonts/ {
        alias /root/workspace/love/fonts/;
        try_files $uri =404;

        # Set proper MIME types for fonts
        location ~* \.(ttf|otf|woff|woff2|eot)$ {
            add_header Access-Control-Allow-Origin "*";
            add_header Cache-Control "public, max-age=31536000, immutable";
            expires 1y;
        }
    }

    # Love site fallback backgrounds CSS
    location /love/fallback-backgrounds.css {
        alias /root/workspace/love/fallback-backgrounds.css;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Content-Type "text/css";
    }

    # Love site video background handler JS
    location /love/video-background-handler.js {
        alias /root/workspace/love/video-background-handler.js;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Content-Type "application/javascript";
    }

    # Love site background files (videos, images, etc.)
    location /love/background/ {
        alias /root/workspace/love/background/;
        try_files $uri $uri/ =404;

        # Cache media files
        location ~* \.(mp4|webm|ogg|avi|mov|wmv|flv|mkv|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            # 支持大文件流式传输
            add_header Accept-Ranges bytes;
        }
    }

    # Love site API routes
    location /love/api/ {
        # Remove /love prefix and pass to backend
        rewrite ^/love/api/(.*) /api/$1 break;
        
        proxy_pass http://love_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # CORS headers
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Love site test files
    location /love/test/ {
        alias /root/workspace/love/test/;
        try_files $uri $uri/ =404;
    }

    # ===== NEW-API CONFIGURATIONS ===== ✨ 支持AI路径功能

    # ===== GPT-LOAD CONFIGURATIONS ===== ✨ GPT-Load AI代理服务
    
    # Handle /lgpt-load without trailing slash
    location = /lgpt-load {
        return 301 $scheme://$host/lgpt-load/;
    }
    
    # GPT-Load main interface
    location /lgpt-load/ {
        # Remove /lgpt-load prefix before forwarding to backend
        rewrite ^/lgpt-load/(.*) /$1 break;
        
        # Forward to gpt-load backend
        proxy_pass http://127.0.0.1:3001;
        
        # Standard proxy headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # Buffer settings for streaming responses
        proxy_buffering off;
        proxy_cache off;
        
        # Handle large request bodies
        client_max_body_size 100M;
        
        # Security headers for GPT-Load
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
    }

    # ===== AI PATH CONFIGURATIONS ===== ✨ 新增AI路径功能

    # AI path static files (前端资源)
    location /ai/assets/ {
        alias /root/workspace/new-api/web/dist/assets/;
        try_files $uri $uri/ =404;

        # Cache static assets
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # AI path frontend (前端页面)
    location /ai/ {
        alias /root/workspace/new-api/web/dist/;
        try_files $uri $uri/ /index.html;

        # Set proper MIME types
        location ~* \.html$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
    }

    # AI path API routes
    location /ai/api/ {
        # Remove /ai prefix before forwarding to backend
        rewrite ^/ai/(.*) /$1 break;

        # Forward to new-api backend
        proxy_pass http://new_api_backend;

        # Preserve original host and protocol information
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Add custom header to indicate the original path prefix
        proxy_set_header X-Original-Path-Prefix /ai;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # Buffer settings for streaming responses
        proxy_buffering off;
        proxy_cache off;
    }

    # AI path OpenAI API routes
    location /ai/v1/ {
        # Remove /ai prefix before forwarding to backend
        rewrite ^/ai/(.*) /$1 break;

        # Forward to new-api backend
        proxy_pass http://new_api_backend;

        # Preserve original host and protocol information
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Add custom header to indicate the original path prefix
        proxy_set_header X-Original-Path-Prefix /ai;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # Buffer settings for streaming responses
        proxy_buffering off;
        proxy_cache off;
    }

    # Handle /ai without trailing slash
    location = /ai {
        return 301 $scheme://$host/ai/;
    }

    # New API service at /new-api path (for backward compatibility)
    location /new-api/ {
        # Remove /new-api prefix before forwarding to backend
        rewrite ^/new-api/(.*) /$1 break;

        # Forward to new-api backend
        proxy_pass http://new_api_backend;

        # Preserve original host and protocol information
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Add custom header to indicate the original path prefix
        proxy_set_header X-Original-Path-Prefix /new-api;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # Buffer settings for streaming responses
        proxy_buffering off;
        proxy_cache off;
    }

    # Handle /new-api without trailing slash
    location = /new-api {
        return 301 $scheme://$host/new-api/;
    }

    # Main site root - New API service (direct access)
    location / {
        # Forward to new-api backend
        proxy_pass http://new_api_backend;

        # Preserve original host and protocol information
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # Buffer settings for streaming responses
        proxy_buffering off;
        proxy_cache off;
    }
}
