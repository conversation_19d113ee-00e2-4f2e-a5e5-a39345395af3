# Nginx Configuration Snippets

This directory contains reusable nginx configuration snippets that implement modern best practices for security, performance, and functionality.

## Available Snippets

### Core Configuration
- **`ssl-params.conf`** - Modern SSL/TLS configuration with secure ciphers and protocols
- **`proxy-params.conf`** - Standard reverse proxy settings with WebSocket support
- **`performance.conf`** - Performance optimization settings (gzip, buffers, timeouts)
- **`log-formats.conf`** - Standardized log formats for different use cases

### Security
- **`security-headers.conf`** - Essential security headers (HSTS, CSP, X-Frame-Options, etc.)
- **`security-enhanced.conf`** - Advanced security measures (request filtering, rate limiting zones)
- **`cors-headers.conf`** - Cross-Origin Resource Sharing configuration

### Content Serving
- **`static-files.conf`** - Optimized static file serving with caching and MIME types
- **`cache-static.conf`** - Static content caching strategies
- **`api-common.conf`** - Common API endpoint configuration

### Monitoring & Operations
- **`monitoring.conf`** - Health checks, status endpoints, and metrics
- **`log-formats.conf`** - Structured logging formats (JSON, detailed, security)

## Usage Guidelines

### Basic Usage
Include snippets in your server blocks:
```nginx
server {
    listen 443 ssl http2;
    server_name example.com;
    
    # Include SSL configuration
    include /root/workspace/shared/nginx/snippets/ssl-params.conf;
    
    # Include security headers
    include /root/workspace/shared/nginx/snippets/security-headers.conf;
    
    location / {
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        proxy_pass http://backend;
    }
}
```

### HTTP Block Snippets
Some snippets must be included in the http block:
```nginx
http {
    # Include log formats
    include /root/workspace/shared/nginx/snippets/log-formats.conf;
    
    # Include performance settings
    include /root/workspace/shared/nginx/snippets/performance.conf;
    
    server {
        # Server configuration...
    }
}
```

### Snippet Dependencies
- `api-common.conf` requires `cors-headers.conf`
- `static-files.conf` works best with `cache-static.conf`
- `security-enhanced.conf` complements `security-headers.conf`

## Best Practices

1. **Order Matters**: Include snippets in the correct order (security first, then functionality)
2. **Avoid Duplicates**: Don't include conflicting snippets in the same context
3. **Customize**: Comment out or modify settings that don't fit your use case
4. **Test**: Always test configuration changes with `nginx -t`

## Customization

Most snippets include commented examples and alternatives. Uncomment and modify as needed for your specific requirements.

For rate limiting, SSL certificates, and other environment-specific settings, customize the values in your main configuration files.
