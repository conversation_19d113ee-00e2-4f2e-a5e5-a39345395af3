# Common API configuration
# This snippet provides common settings for API endpoints
# Includes CORS, security headers, and API-specific optimizations

# Include CORS headers for API endpoints
include /root/workspace/shared/nginx/snippets/cors-headers.conf;

# API-specific security headers
add_header X-API-Version "1.0" always;
add_header X-RateLimit-Limit "1000" always;
add_header X-Content-Type-Options "nosniff" always;

# Note: Request body size should be set in individual locations as needed
# Example: client_max_body_size 10M;

# Note: Proxy timeout and buffering settings are defined in proxy-params.conf
# For API-specific timeouts, override in the main server configuration if needed

# API request logging (use main format if detailed is not defined)
access_log /var/log/nginx/api.log;

# Note: Preflight requests should be handled in the main server configuration
# Example for handling OPTIONS requests:
# if ($request_method = 'OPTIONS') {
#     add_header 'Access-Control-Allow-Origin' '*' always;
#     add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
#     add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Origin' always;
#     add_header 'Access-Control-Max-Age' 1728000;
#     add_header 'Content-Type' 'text/plain; charset=utf-8';
#     add_header 'Content-Length' 0;
#     return 204;
# }

# Error handling for APIs (to be defined at server level)
# error_page 404 = @api_404;
# error_page 500 502 503 504 = @api_error;

# Note: Named locations should be defined at server level:
# location @api_404 {
#     add_header Content-Type application/json always;
#     return 404 '{"error":"Not Found","message":"The requested API endpoint was not found"}';
# }
#
# location @api_error {
#     add_header Content-Type application/json always;
#     return 500 '{"error":"Internal Server Error","message":"An error occurred while processing your request"}';
# }
