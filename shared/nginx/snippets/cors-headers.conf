# CORS (Cross-Origin Resource Sharing) headers
# This snippet provides CORS headers for API endpoints
# Allows cross-origin requests from web applications

# Allow all origins (customize as needed for production)
add_header 'Access-Control-Allow-Origin' '*' always;

# Allow common HTTP methods
add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;

# Allow common headers
add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Origin' always;

# Expose headers to the client
add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range,X-Total-Count' always;

# Allow credentials (set to false if not needed)
add_header 'Access-Control-Allow-Credentials' 'true' always;

# Note: Preflight requests should be handled in the main server configuration
# Example for handling OPTIONS requests:
# if ($request_method = 'OPTIONS') {
#     add_header 'Access-Control-Allow-Origin' '*' always;
#     add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
#     add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Origin' always;
#     add_header 'Access-Control-Max-Age' 1728000;
#     add_header 'Content-Type' 'text/plain; charset=utf-8';
#     add_header 'Content-Length' 0;
#     return 204;
# }
