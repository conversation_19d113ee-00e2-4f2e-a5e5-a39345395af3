# Nginx log formats configuration
# This snippet provides standardized log formats for different use cases
# Helps with monitoring, debugging, and analytics

# Standard combined log format with additional fields
log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                '$status $body_bytes_sent "$http_referer" '
                '"$http_user_agent" "$http_x_forwarded_for"';

# Detailed log format with timing information
log_format detailed '$remote_addr - $remote_user [$time_local] '
                   '"$request" $status $body_bytes_sent '
                   '"$http_referer" "$http_user_agent" '
                   '$request_time $upstream_response_time '
                   '$upstream_connect_time $upstream_header_time';

# JSON log format for structured logging
log_format json escape=json '{'
                '"timestamp":"$time_iso8601",'
                '"remote_addr":"$remote_addr",'
                '"remote_user":"$remote_user",'
                '"request":"$request",'
                '"status":$status,'
                '"body_bytes_sent":$body_bytes_sent,'
                '"http_referer":"$http_referer",'
                '"http_user_agent":"$http_user_agent",'
                '"http_x_forwarded_for":"$http_x_forwarded_for",'
                '"request_time":$request_time,'
                '"upstream_response_time":"$upstream_response_time",'
                '"upstream_connect_time":"$upstream_connect_time",'
                '"upstream_header_time":"$upstream_header_time"'
                '}';

# Security log format for monitoring suspicious activities
log_format security '$time_iso8601 $remote_addr "$request" $status '
                   '$body_bytes_sent "$http_user_agent" '
                   '$request_time "$http_x_forwarded_for"';

# Performance log format for optimization
log_format performance '$time_iso8601 $request_time $upstream_response_time '
                      '$upstream_connect_time $upstream_header_time '
                      '$request "$status" $body_bytes_sent';

# Note: These log formats should be defined in the http block of nginx.conf
# Usage example:
# access_log /var/log/nginx/access.log main;
# access_log /var/log/nginx/detailed.log detailed;
