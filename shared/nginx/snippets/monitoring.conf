# Nginx monitoring and status configuration
# This snippet provides monitoring endpoints and status information
# Useful for health checks, metrics collection, and system monitoring

# Nginx status endpoint for monitoring
location /nginx_status {
    stub_status on;
    access_log off;
    
    # Restrict access to monitoring systems
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    allow **********/12;
    allow ***********/16;
    deny all;
}

# Health check endpoint
location /health {
    access_log off;
    return 200 "healthy\n";
    add_header Content-Type text/plain;
}

# Basic metrics endpoint
location /metrics {
    access_log off;
    return 200 "# Nginx basic metrics\nnginx_up 1\n";
    add_header Content-Type text/plain;
    
    # Restrict access to monitoring systems
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    allow **********/12;
    allow ***********/16;
    deny all;
}

# Server info endpoint (for debugging)
location /server-info {
    access_log off;
    return 200 "Server: $hostname\nTime: $time_iso8601\nRequest ID: $request_id\n";
    add_header Content-Type text/plain;
    
    # Restrict access to localhost only
    allow 127.0.0.1;
    deny all;
}
