# Nginx performance optimization configuration
# This snippet provides performance tuning settings
# Optimizes for high-traffic scenarios and better resource utilization

# Gzip compression settings
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_proxied any;
gzip_comp_level 6;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/javascript
    application/xml+rss
    application/json
    application/xml
    image/svg+xml;

# Brotli compression (if module is available)
# brotli on;
# brotli_comp_level 6;
# brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# Client request settings
client_max_body_size 100M;
client_body_buffer_size 128k;
client_header_buffer_size 1k;
large_client_header_buffers 4 4k;

# Timeout settings
client_body_timeout 12;
client_header_timeout 12;
keepalive_timeout 65;
keepalive_requests 100;
send_timeout 10;

# Buffer settings
proxy_buffering on;
proxy_buffer_size 128k;
proxy_buffers 4 256k;
proxy_busy_buffers_size 256k;

# File handling
sendfile on;
tcp_nopush on;
tcp_nodelay on;

# Open file cache
open_file_cache max=1000 inactive=20s;
open_file_cache_valid 30s;
open_file_cache_min_uses 2;
open_file_cache_errors on;

# Rate limiting (basic setup)
# limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
# limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

# Connection limiting
# limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
# limit_conn conn_limit_per_ip 20;
