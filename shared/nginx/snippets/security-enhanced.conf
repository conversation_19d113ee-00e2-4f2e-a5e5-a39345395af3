# Enhanced security configuration
# This snippet provides additional security measures beyond basic security headers
# Includes rate limiting, request filtering, and advanced security policies

# Note: server_tokens off is defined in security-headers.conf

# Prevent access to backup and temporary files
location ~* \.(bak|backup|old|orig|tmp|temp|~)$ {
    deny all;
    return 404;
}

# Block common exploit attempts
location ~* \.(php|asp|aspx|jsp|cgi)$ {
    deny all;
    return 404;
}

# Block SQL injection attempts
if ($args ~* "(\<|%3C).*script.*(\>|%3E)") {
    return 403;
}
if ($args ~* "GLOBALS(=|\[|\%[0-9A-Z]{0,2})") {
    return 403;
}
if ($args ~* "_REQUEST(=|\[|\%[0-9A-Z]{0,2})") {
    return 403;
}

# Block XSS attempts
if ($args ~* "(\<|%3C).*iframe.*(\>|%3E)") {
    return 403;
}

# Limit request methods
if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE|OPTIONS|PATCH)$ ) {
    return 405;
}

# Block requests with suspicious user agents
if ($http_user_agent ~* (nmap|nikto|wikto|sf|sqlmap|bsqlbf|w3af|acunetix|havij|appscan)) {
    return 403;
}

# Block requests with no user agent
if ($http_user_agent = "") {
    return 403;
}

# Rate limiting zones (to be defined in http block)
# limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
# limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
# limit_req_zone $binary_remote_addr zone=api:10m rate=30r/s;

# Apply rate limiting (uncomment as needed)
# limit_req zone=general burst=20 nodelay;

# Connection limiting
# limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
# limit_conn conn_limit_per_ip 20;

# Additional security headers
add_header X-Frame-Options "DENY" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;

# Content Security Policy (customize as needed)
# add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none';" always;
