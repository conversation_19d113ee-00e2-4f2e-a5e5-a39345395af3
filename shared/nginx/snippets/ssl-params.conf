# Modern SSL/TLS configuration
# This snippet provides secure SSL/TLS settings for HTTPS connections
# Compatible with modern browsers and security best practices

# SSL protocols - only modern versions
ssl_protocols TLSv1.2 TLSv1.3;

# SSL ciphers - modern secure cipher suites (updated for better security)
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;

# SSL session settings
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_session_tickets off;

# OCSP stapling
ssl_stapling on;
ssl_stapling_verify on;

# SSL optimization
ssl_buffer_size 8k;

# Perfect Forward Secrecy (updated for better compatibility)
ssl_ecdh_curve X25519:prime256v1:secp384r1;

# SSL security enhancements
ssl_early_data off;

# Additional SSL optimizations (uncomment if you have the key file)
# ssl_session_ticket_key /etc/nginx/ssl_session_ticket.key;

# Note: SSL certificate paths should be set in the main server block:
# ssl_certificate /etc/letsencrypt/live/DOMAIN/fullchain.pem;
# ssl_certificate_key /etc/letsencrypt/live/DOMAIN/privkey.pem;

# Additional security headers (can be overridden in main config)
# add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
