# Static files serving configuration
# This snippet provides optimized settings for serving static files
# Includes caching, compression, and security settings

# Enable efficient file serving
sendfile on;
tcp_nopush on;
tcp_nodelay on;

# Static file caching with ETag support
etag on;

# Serve static files with appropriate MIME types
location ~* \.(css|js|map)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary "Accept-Encoding";
    
    # Enable gzip for text files
    gzip_static on;
    
    # Security headers for static files
    add_header X-Content-Type-Options "nosniff" always;
}

# Images with long cache
location ~* \.(png|jpg|jpeg|gif|ico|svg|webp|avif)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    
    # Allow cross-origin requests for images
    add_header Access-Control-Allow-Origin "*";
    
    # Optimize image serving
    add_header Vary "Accept";
}

# Fonts with CORS support
location ~* \.(woff|woff2|ttf|eot|otf)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Access-Control-Allow-Origin "*";
    add_header Access-Control-Allow-Methods "GET, OPTIONS";
    add_header Access-Control-Allow-Headers "Range";
}

# Media files with range support
location ~* \.(mp4|webm|ogg|avi|mov|wmv|flv|mkv|mp3|wav|ogg)$ {
    expires 1M;
    add_header Cache-Control "public";
    add_header Accept-Ranges "bytes";

    # Enable partial content for media streaming
    # Note: Content-Range header is automatically set by nginx for range requests
}

# Documents with moderate caching
location ~* \.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|tar|gz)$ {
    expires 1w;
    add_header Cache-Control "public";
    
    # Force download for certain file types
    add_header Content-Disposition "attachment";
}

# Prevent access to sensitive files
location ~* \.(htaccess|htpasswd|ini|log|sh|sql|conf)$ {
    deny all;
    return 404;
}

# Prevent access to hidden files
location ~ /\. {
    deny all;
    return 404;
}
