# Nginx Configuration Templates

This directory contains nginx configuration templates for different types of applications and services. Each template is optimized for specific use cases and follows modern nginx best practices.

## Available Templates

### 1. **project.tpl** - General Purpose Template
- **Use Case**: Mixed applications with both static content and API endpoints
- **Best For**: Traditional web applications, full-stack projects
- **Features**: HTTP to HTTPS redirect, static file serving, API proxy, health checks

### 2. **spa.tpl** - Single Page Application Template
- **Use Case**: React, Vue, Angular, and other SPA frameworks
- **Best For**: Client-side routed applications
- **Features**: Client-side routing support, PWA optimization, service worker handling, source map blocking

### 3. **api.tpl** - API Service Template
- **Use Case**: RESTful APIs, GraphQL services, microservices
- **Best For**: Backend API services
- **Features**: Rate limiting, API versioning, WebSocket support, structured error responses

### 4. **microservice.tpl** - Microservice Template
- **Use Case**: Microservice architecture with load balancing
- **Best For**: Distributed systems, service mesh
- **Features**: Load balancing, health checks, circuit breaker, service discovery

### 5. **static.tpl** - Static Website Template
- **Use Case**: Static HTML websites, documentation sites
- **Best For**: Jekyll, Hugo, Gatsby static sites
- **Features**: Aggressive caching, WebP support, SEO optimization

## Template Placeholders

All templates use the following placeholder system:

### Common Placeholders
- `__PROJECT_NAME__` - Project identifier (e.g., my-app, user-service)
- `__DOMAIN__` - Domain name (e.g., example.com, api.example.com)
- `__PROJECT_ROOT__` - Project root directory (e.g., /var/www/my-app)
- `__LOCATION_PATH__` - URL path prefix (e.g., /, /api/, /app/)

### Backend Placeholders
- `__BACKEND_HOST__` - Backend server host (e.g., 127.0.0.1, backend-server)
- `__BACKEND_PORT__` - Backend server port (e.g., 3000, 8080)
- `__BACKEND_PORT_2__` - Secondary backend port (for load balancing)
- `__BACKEND_PORT_3__` - Tertiary backend port (for load balancing)

### Optional Placeholders
- `__BACKUP_HOST__` - Backup server host (for microservices)
- `__BACKUP_PORT__` - Backup server port (for microservices)

## Usage Examples

### Using the nginx-manager.sh Script
```bash
# Generate configuration from template
./nginx-manager.sh create-from-template spa my-spa-app example.com

# Generate with custom parameters
./nginx-manager.sh create-from-template api my-api api.example.com \
  --backend-host=127.0.0.1 \
  --backend-port=3000 \
  --location-path=/api/v1/
```

### Manual Template Usage
```bash
# Copy template
cp /root/workspace/shared/nginx/templates/spa.tpl /tmp/my-app.conf

# Replace placeholders
sed -i 's/__PROJECT_NAME__/my-app/g' /tmp/my-app.conf
sed -i 's/__DOMAIN__/example.com/g' /tmp/my-app.conf
sed -i 's|__PROJECT_ROOT__|/var/www/my-app|g' /tmp/my-app.conf
sed -i 's|__LOCATION_PATH__|/|g' /tmp/my-app.conf

# Deploy configuration
sudo cp /tmp/my-app.conf /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/my-app.conf /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx
```

## Template Selection Guide

### Choose **project.tpl** when:
- Building a traditional web application
- Need both static content and API endpoints
- Migrating from existing nginx configurations
- Unsure about specific requirements

### Choose **spa.tpl** when:
- Building React, Vue, Angular applications
- Need client-side routing support
- Implementing Progressive Web App (PWA)
- Serving from a build directory (dist/, build/)

### Choose **api.tpl** when:
- Building pure API services
- Need advanced rate limiting
- Implementing RESTful or GraphQL APIs
- Require structured error responses

### Choose **microservice.tpl** when:
- Implementing microservice architecture
- Need load balancing across multiple instances
- Require health checks and circuit breakers
- Building distributed systems

### Choose **static.tpl** when:
- Serving static HTML websites
- Building documentation sites
- Using static site generators (Jekyll, Hugo)
- Need aggressive caching for performance

## Customization

### Adding Custom Locations
Add new location blocks before the error pages section:
```nginx
# Custom location
location /custom/ {
    # Your custom configuration
    proxy_pass http://custom-backend;
    include /root/workspace/shared/nginx/snippets/proxy-params.conf;
}
```

### Modifying Security Settings
Uncomment or modify security-related configurations:
```nginx
# Enable rate limiting
limit_req zone=api_general burst=100 nodelay;

# Add custom security headers
add_header X-Custom-Header "value" always;
```

### SSL Certificate Paths
Templates assume Let's Encrypt certificates. Modify paths if using different certificates:
```nginx
ssl_certificate /path/to/your/certificate.pem;
ssl_certificate_key /path/to/your/private.key;
```

## Best Practices

1. **Always test configurations**: Use `nginx -t` before applying
2. **Use version control**: Keep generated configurations in version control
3. **Monitor logs**: Check nginx error and access logs after deployment
4. **Regular updates**: Update templates when nginx best practices evolve
5. **Security first**: Review security settings for your specific use case

## Integration with Snippets

All templates are designed to work with the configuration snippets in `/root/workspace/shared/nginx/snippets/`. They automatically include:

- SSL parameters and security headers
- Performance optimizations
- Monitoring endpoints
- Static file serving optimizations
- API common settings (where applicable)

For more information about snippets, see `/root/workspace/shared/nginx/snippets/README.md`.
