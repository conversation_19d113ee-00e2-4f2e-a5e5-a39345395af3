# Nginx configuration template for API Service: __PROJECT_NAME__
# Domain: __DOMAIN__
# Generated automatically - do not edit manually
# Optimized for RESTful APIs, GraphQL, and other API services

# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name __DOMAIN__;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

# Upstream for API backend
upstream __PROJECT_NAME___api {
    server __BACKEND_HOST__:__BACKEND_PORT__;
    keepalive 32;
    keepalive_requests 1000;
    keepalive_timeout 60s;
}

# Rate limiting zones for API
limit_req_zone $binary_remote_addr zone=api_general:10m rate=100r/s;
limit_req_zone $binary_remote_addr zone=api_auth:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=api_upload:10m rate=10r/s;

# HTTPS server for API
server {
    listen 443 ssl http2;
    server_name __DOMAIN__;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/__DOMAIN__/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/__DOMAIN__/privkey.pem;
    
    # Include SSL parameters
    include /root/workspace/shared/nginx/snippets/ssl-params.conf;
    
    # Include security headers
    include /root/workspace/shared/nginx/snippets/security-headers.conf;
    
    # Include enhanced security for APIs
    include /root/workspace/shared/nginx/snippets/security-enhanced.conf;
    
    # Include monitoring endpoints
    include /root/workspace/shared/nginx/snippets/monitoring.conf;

    # API root location
    location __LOCATION_PATH__ {
        # Apply general rate limiting
        limit_req zone=api_general burst=200 nodelay;
        
        # Remove path prefix if needed
        # rewrite ^__LOCATION_PATH__(.*) /$1 break;
        
        # Proxy to API backend
        proxy_pass http://__PROJECT_NAME___api;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Include API common settings
        include /root/workspace/shared/nginx/snippets/api-common.conf;
        
        # API-specific headers
        proxy_set_header X-API-Key $http_x_api_key;
        proxy_set_header X-Client-IP $remote_addr;
        proxy_set_header X-Request-ID $request_id;
        
        # Enable request/response logging for APIs
        access_log /var/log/nginx/__PROJECT_NAME___api.log detailed;
    }

    # Authentication endpoints with stricter rate limiting
    location __LOCATION_PATH__auth/ {
        # Apply auth rate limiting
        limit_req zone=api_auth burst=10 nodelay;
        
        # Remove path prefix
        rewrite ^__LOCATION_PATH__auth/(.*) /auth/$1 break;
        
        # Proxy to API backend
        proxy_pass http://__PROJECT_NAME___api;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Include API common settings
        include /root/workspace/shared/nginx/snippets/api-common.conf;
        
        # Security logging for auth endpoints
        access_log /var/log/nginx/__PROJECT_NAME___auth.log security;
    }

    # File upload endpoints
    location __LOCATION_PATH__upload/ {
        # Apply upload rate limiting
        limit_req zone=api_upload burst=20 nodelay;
        
        # Increase body size for uploads
        client_max_body_size 100M;
        client_body_timeout 300s;
        
        # Remove path prefix
        rewrite ^__LOCATION_PATH__upload/(.*) /upload/$1 break;
        
        # Proxy to API backend
        proxy_pass http://__PROJECT_NAME___api;
        
        # Upload-specific proxy settings (override proxy-params defaults)
        proxy_request_buffering off;

        # Include proxy parameters (contains default timeouts)
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;

        # Note: For custom upload timeouts, modify proxy-params.conf or override in server block
        
        # Include API common settings
        include /root/workspace/shared/nginx/snippets/api-common.conf;
    }

    # WebSocket endpoints (if API supports real-time features)
    location __LOCATION_PATH__ws/ {
        # Remove path prefix
        rewrite ^__LOCATION_PATH__ws/(.*) /ws/$1 break;

        # Proxy to API backend
        proxy_pass http://__PROJECT_NAME___api;

        # WebSocket specific settings (override proxy-params defaults)
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Include proxy parameters (contains proxy_http_version 1.1)
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;

        # Note: For WebSocket timeouts, consider modifying proxy-params.conf or using separate upstream
    }

    # API documentation
    location __LOCATION_PATH__docs {
        # No rate limiting for documentation
        
        # Proxy to API backend
        proxy_pass http://__PROJECT_NAME___api/docs;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Cache documentation for better performance
        proxy_cache_valid 200 1h;
        proxy_cache_valid 404 1m;
    }

    # API health check (custom endpoint, different from monitoring.conf /health)
    location __LOCATION_PATH__api-health {
        # Proxy to health endpoint
        proxy_pass http://__PROJECT_NAME___api/health;
        
        # Health check specific proxy settings
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Quick health check timeouts
        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;
        
        access_log off;
    }

    # API version endpoint
    location __LOCATION_PATH__version {
        # Proxy to version endpoint
        proxy_pass http://__PROJECT_NAME___api/version;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Cache version info
        proxy_cache_valid 200 1h;
        
        access_log off;
    }

    # Block non-API requests (only if API is not at root path)
    # Uncomment if __LOCATION_PATH__ is not "/"
    # location / {
    #     return 404 '{"error":"Not Found","message":"This is an API server. Please use the correct API endpoints."}';
    #     add_header Content-Type application/json always;
    # }

    # Custom error pages for API
    error_page 404 = @api_404;
    error_page 429 = @api_rate_limit;
    error_page 500 502 503 504 = @api_error;
    
    location @api_404 {
        add_header Content-Type application/json always;
        return 404 '{"error":"Not Found","message":"The requested API endpoint was not found"}';
    }
    
    location @api_rate_limit {
        add_header Content-Type application/json always;
        return 429 '{"error":"Rate Limit Exceeded","message":"Too many requests. Please try again later."}';
    }
    
    location @api_error {
        add_header Content-Type application/json always;
        return 500 '{"error":"Internal Server Error","message":"The API service is temporarily unavailable"}';
    }
}

# Template placeholders for API Service:
# __PROJECT_NAME__ - API service name (e.g., main-api, user-api)
# __DOMAIN__ - Domain name (e.g., api.example.com)
# __BACKEND_HOST__ - Backend host (e.g., 127.0.0.1)
# __BACKEND_PORT__ - Backend port (e.g., 3000)
# __LOCATION_PATH__ - API base path (e.g., /api/v1/, /graphql/)
#
# Usage: Replace placeholders with actual values when generating configuration
# Note: Adjust rate limiting values based on your API requirements
