# Nginx configuration template for Microservice: __PROJECT_NAME__
# Domain: __DOMAIN__
# Generated automatically - do not edit manually
# Optimized for microservice architecture with load balancing and health checks

# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name __DOMAIN__;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

# Upstream configuration for microservice instances
upstream __PROJECT_NAME___backend {
    # Load balancing method (least_conn, ip_hash, or round_robin)
    least_conn;
    
    # Primary service instances
    server __BACKEND_HOST__:__BACKEND_PORT__ max_fails=3 fail_timeout=30s;
    # server __BACKEND_HOST__:__BACKEND_PORT_2__ max_fails=3 fail_timeout=30s;
    # server __BACKEND_HOST__:__BACKEND_PORT_3__ max_fails=3 fail_timeout=30s;
    
    # Backup instance (optional)
    # server __BACKUP_HOST__:__BACKUP_PORT__ backup;
    
    # Connection pooling
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# HTTPS server for microservice
server {
    listen 443 ssl http2;
    server_name __DOMAIN__;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/__DOMAIN__/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/__DOMAIN__/privkey.pem;
    
    # Include SSL parameters
    include /root/workspace/shared/nginx/snippets/ssl-params.conf;
    
    # Include security headers
    include /root/workspace/shared/nginx/snippets/security-headers.conf;
    
    # Include enhanced security for microservices
    include /root/workspace/shared/nginx/snippets/security-enhanced.conf;
    
    # Include monitoring endpoints
    include /root/workspace/shared/nginx/snippets/monitoring.conf;

    # Main microservice location
    location __LOCATION_PATH__ {
        # Remove path prefix if needed
        # rewrite ^__LOCATION_PATH__(.*) /$1 break;
        
        # Proxy to microservice backend
        proxy_pass http://__PROJECT_NAME___backend;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Include API common settings
        include /root/workspace/shared/nginx/snippets/api-common.conf;
        
        # Microservice-specific headers
        proxy_set_header X-Service-Name "__PROJECT_NAME__";
        proxy_set_header X-Request-ID $request_id;
        proxy_set_header X-Correlation-ID $http_x_correlation_id;
        
        # Circuit breaker settings
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 10s;
    }

    # Health check endpoint (bypass load balancer, different from monitoring.conf /health)
    location __LOCATION_PATH__service-health {
        # Direct health check to first backend
        proxy_pass http://__BACKEND_HOST__:__BACKEND_PORT__/health;
        
        # Health check specific proxy settings
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Health check specific timeouts
        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;

        # No caching for health checks
        proxy_cache off;
        proxy_buffering off;
        
        access_log off;
    }

    # Service metrics endpoint (different from monitoring.conf /metrics)
    location __LOCATION_PATH__service-metrics {
        # Proxy to metrics endpoint
        proxy_pass http://__PROJECT_NAME___backend/metrics;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Restrict access to monitoring systems
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
        
        access_log off;
    }

    # API documentation (if available)
    location __LOCATION_PATH__docs {
        # Proxy to documentation endpoint
        proxy_pass http://__PROJECT_NAME___backend/docs;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Optional: Restrict access to internal networks
        # allow 10.0.0.0/8;
        # deny all;
    }

    # Static assets (if any)
    location __LOCATION_PATH__static/ {
        # Proxy to backend for dynamic static serving
        proxy_pass http://__PROJECT_NAME___backend;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Cache static assets
        proxy_cache_valid 200 1h;
        proxy_cache_valid 404 1m;
    }

    # Error pages
    error_page 404 = @microservice_404;
    error_page 500 502 503 504 = @microservice_error;
    
    location @microservice_404 {
        add_header Content-Type application/json always;
        return 404 '{"error":"Not Found","service":"__PROJECT_NAME__","message":"The requested endpoint was not found"}';
    }
    
    location @microservice_error {
        add_header Content-Type application/json always;
        return 500 '{"error":"Service Unavailable","service":"__PROJECT_NAME__","message":"The microservice is temporarily unavailable"}';
    }
}

# Template placeholders for Microservice:
# __PROJECT_NAME__ - Microservice name (e.g., user-service, order-service)
# __DOMAIN__ - Domain name (e.g., api.example.com)
# __BACKEND_HOST__ - Primary backend host (e.g., 127.0.0.1)
# __BACKEND_PORT__ - Primary backend port (e.g., 3000)
# __BACKEND_PORT_2__ - Secondary backend port (e.g., 3001) [optional]
# __BACKEND_PORT_3__ - Tertiary backend port (e.g., 3002) [optional]
# __BACKUP_HOST__ - Backup host (e.g., 127.0.0.1) [optional]
# __BACKUP_PORT__ - Backup port (e.g., 3010) [optional]
# __LOCATION_PATH__ - Location path (e.g., /api/users/, /api/orders/)
#
# Usage: Replace placeholders with actual values when generating configuration
# Note: Uncomment additional server lines in upstream block for multiple instances
