# Nginx configuration template for __PROJECT_NAME__
# Domain: __DOMAIN__
# Generated automatically - do not edit manually

# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name __DOMAIN__;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

# Upstream for __PROJECT_NAME__ backend
upstream __PROJECT_NAME___backend {
    server __BACKEND_HOST__:__BACKEND_PORT__;
    keepalive 32;
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name __DOMAIN__;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/__DOMAIN__/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/__DOMAIN__/privkey.pem;
    
    # Include SSL parameters
    include /root/workspace/shared/nginx/snippets/ssl-params.conf;
    
    # Include security headers
    include /root/workspace/shared/nginx/snippets/security-headers.conf;

    # Include monitoring endpoints
    include /root/workspace/shared/nginx/snippets/monitoring.conf;

    # Root directory
    root __PROJECT_ROOT__;
    index index.html index.htm;

    # Include static file serving optimization
    include /root/workspace/shared/nginx/snippets/static-files.conf;

    # Static files with caching
    include /root/workspace/shared/nginx/snippets/cache-static.conf;

    # Main application location
    location __LOCATION_PATH__ {
        # Remove path prefix if needed
        # rewrite ^__LOCATION_PATH__(.*) /$1 break;
        
        # Proxy to backend
        proxy_pass http://__PROJECT_NAME___backend;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Include CORS headers if needed for API
        # include /root/workspace/shared/nginx/snippets/cors-headers.conf;
    }

    # API endpoints (if applicable)
    location __LOCATION_PATH__api/ {
        # Remove path prefix
        rewrite ^__LOCATION_PATH__api/(.*) /api/$1 break;
        
        # Proxy to backend
        proxy_pass http://__PROJECT_NAME___backend;
        
        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;
        
        # Include API common settings
        include /root/workspace/shared/nginx/snippets/api-common.conf;
    }

    # Static assets location (if applicable)
    location __LOCATION_PATH__assets/ {
        alias __PROJECT_ROOT__/assets/;
        try_files $uri $uri/ =404;
        
        # Include static file caching
        include /root/workspace/shared/nginx/snippets/cache-static.conf;
    }

    # Application health check endpoint (different from monitoring.conf /health)
    location __LOCATION_PATH__app-health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# Template placeholders:
# __PROJECT_NAME__ - Project name (e.g., new-api, love, gpt-load)
# __DOMAIN__ - Domain name (e.g., liangliangdamowang.edu.deal)
# __BACKEND_HOST__ - Backend host (e.g., 127.0.0.1)
# __BACKEND_PORT__ - Backend port (e.g., 3000, 1314, 3001)
# __PROJECT_ROOT__ - Project root directory
# __LOCATION_PATH__ - Location path (e.g., /, /love/, /lgpt-load/)
