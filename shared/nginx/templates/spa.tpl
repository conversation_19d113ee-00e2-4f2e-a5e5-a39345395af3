# Nginx configuration template for SPA (Single Page Application)
# Project: __PROJECT_NAME__
# Domain: __DOMAIN__
# Optimized for Vue.js, React, Angular applications

# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name __DOMAIN__;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS server for SPA
server {
    listen 443 ssl http2;
    server_name __DOMAIN__;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/__DOMAIN__/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/__DOMAIN__/privkey.pem;
    
    # Include SSL parameters
    include /root/workspace/shared/nginx/snippets/ssl-params.conf;
    
    # Include security headers
    include /root/workspace/shared/nginx/snippets/security-headers.conf;

    # Include CORS headers for SPA
    include /root/workspace/shared/nginx/snippets/cors-headers.conf;

    # Include monitoring endpoints
    include /root/workspace/shared/nginx/snippets/monitoring.conf;

    # Root directory for SPA
    root __PROJECT_ROOT__/dist;
    index index.html;

    # Include static file serving optimization
    include /root/workspace/shared/nginx/snippets/static-files.conf;

    # SPA main location - handle client-side routing
    location __LOCATION_PATH__ {
        try_files $uri $uri/ __LOCATION_PATH__index.html;

        # Cache HTML files for short time (better than no cache)
        location ~* \.html$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }
    }

    # Static assets with aggressive caching
    location __LOCATION_PATH__assets/ {
        alias __PROJECT_ROOT__/dist/assets/;
        try_files $uri =404;
        
        # Cache static assets for 1 year
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # CORS for fonts
        location ~* \.(woff|woff2|ttf|eot|otf)$ {
            add_header Access-Control-Allow-Origin "*";
        }
    }

    # API proxy (if backend exists)
    location __LOCATION_PATH__api/ {
        # Remove path prefix
        rewrite ^__LOCATION_PATH__api/(.*) /api/$1 break;

        # Proxy to backend
        proxy_pass http://__BACKEND_HOST__:__BACKEND_PORT__;

        # Include proxy parameters
        include /root/workspace/shared/nginx/snippets/proxy-params.conf;

        # Include API common settings
        include /root/workspace/shared/nginx/snippets/api-common.conf;
    }

    # Service worker (no cache)
    location __LOCATION_PATH__sw.js {
        expires off;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }

    # Manifest and other PWA files
    location ~* __LOCATION_PATH__(manifest\.json|browserconfig\.xml)$ {
        expires 1w;
        add_header Cache-Control "public";
    }

    # Security: Block access to source maps in production
    location ~* \.map$ {
        deny all;
        return 404;
    }

    # Note: Gzip compression is handled by performance.conf snippet

    # Error pages
    error_page 404 __LOCATION_PATH__index.html;
    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# Template placeholders:
# __PROJECT_NAME__ - Project name
# __DOMAIN__ - Domain name
# __PROJECT_ROOT__ - Project root directory
# __LOCATION_PATH__ - Location path (e.g., /, /app/, /admin/)
# __BACKEND_HOST__ - Backend host (optional)
# __BACKEND_PORT__ - Backend port (optional)
