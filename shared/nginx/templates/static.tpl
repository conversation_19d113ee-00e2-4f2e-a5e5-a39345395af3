# Nginx configuration template for Static Website: __PROJECT_NAME__
# Domain: __DOMAIN__
# Generated automatically - do not edit manually
# Optimized for static HTML, CSS, JS websites and documentation sites

# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name __DOMAIN__;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS server for static website
server {
    listen 443 ssl http2;
    server_name __DOMAIN__;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/__DOMAIN__/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/__DOMAIN__/privkey.pem;
    
    # Include SSL parameters
    include /root/workspace/shared/nginx/snippets/ssl-params.conf;
    
    # Include security headers
    include /root/workspace/shared/nginx/snippets/security-headers.conf;
    
    # Include monitoring endpoints
    include /root/workspace/shared/nginx/snippets/monitoring.conf;

    # Root directory for static files
    root __PROJECT_ROOT__;
    index index.html index.htm;

    # Include static file serving optimization
    include /root/workspace/shared/nginx/snippets/static-files.conf;
    
    # Include static content caching
    include /root/workspace/shared/nginx/snippets/cache-static.conf;

    # Main location for static content
    location __LOCATION_PATH__ {
        try_files $uri $uri/ $uri.html =404;
        
        # Custom 404 page if exists
        error_page 404 __LOCATION_PATH__404.html;
    }

    # Documentation or blog posts (if using Jekyll, Hugo, etc.)
    location __LOCATION_PATH__docs/ {
        try_files $uri $uri/ $uri.html $uri/index.html =404;
        
        # Longer cache for documentation
        expires 1d;
        add_header Cache-Control "public, must-revalidate";
    }

    # Assets directory with aggressive caching
    location __LOCATION_PATH__assets/ {
        # Long-term caching for assets
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # Enable gzip for assets
        gzip_static on;
        
        # CORS for fonts and other assets
        location ~* \.(woff|woff2|ttf|eot|otf)$ {
            add_header Access-Control-Allow-Origin "*";
        }
    }

    # Images with optimization
    location __LOCATION_PATH__images/ {
        # Cache images for 1 month
        expires 1M;
        add_header Cache-Control "public";
        
        # Enable WebP serving if available (requires webp_suffix map)
        location ~* \.(png|jpg|jpeg)$ {
            add_header Vary "Accept";
            # Note: Requires webp_suffix map definition in http block
            # try_files $uri$webp_suffix $uri =404;
            try_files $uri =404;
        }
    }

    # Downloads directory
    location __LOCATION_PATH__downloads/ {
        # Moderate caching for downloads
        expires 1w;
        add_header Cache-Control "public";
        
        # Force download for certain file types
        location ~* \.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|tar|gz)$ {
            add_header Content-Disposition "attachment";
        }
    }

    # Sitemap and robots.txt
    location = /sitemap.xml {
        expires 1d;
        add_header Cache-Control "public";
        access_log off;
    }
    
    location = /robots.txt {
        expires 1d;
        add_header Cache-Control "public";
        access_log off;
    }

    # Favicon with long cache
    location = /favicon.ico {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Security: Block access to hidden files and directories
    location ~ /\. {
        deny all;
        return 404;
    }

    # Security: Block access to backup and temporary files
    location ~* \.(bak|backup|old|orig|tmp|temp|~)$ {
        deny all;
        return 404;
    }

    # Security: Block access to configuration files
    location ~* \.(conf|config|ini|log|sql)$ {
        deny all;
        return 404;
    }

    # Custom error pages
    error_page 404 __LOCATION_PATH__404.html;
    error_page 403 __LOCATION_PATH__403.html;
    error_page *********** 504 __LOCATION_PATH__50x.html;
    
    # Fallback error pages if custom ones don't exist
    location = __LOCATION_PATH__404.html {
        internal;
    }
    
    location = __LOCATION_PATH__403.html {
        internal;
    }
    
    location = __LOCATION_PATH__50x.html {
        internal;
    }
}

# Template placeholders for Static Website:
# __PROJECT_NAME__ - Website name (e.g., company-website, documentation)
# __DOMAIN__ - Domain name (e.g., example.com, docs.example.com)
# __PROJECT_ROOT__ - Website root directory (e.g., /var/www/example.com)
# __LOCATION_PATH__ - Location path (e.g., /, /docs/)
#
# Usage: Replace placeholders with actual values when generating configuration
# Note: Create custom error pages (404.html, 403.html, 50x.html) in your website root
